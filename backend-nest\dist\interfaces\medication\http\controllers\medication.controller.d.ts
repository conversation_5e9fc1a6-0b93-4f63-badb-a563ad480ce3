import { CreateMedicationUseCase } from 'src/application/medication/use-cases/create-medication.usecase';
import { CreateMedicationWithAdherenceUseCase } from 'src/application/medication/use-cases/create-medication-with-adherence.usecase';
import { DeleteMedicationUseCase } from 'src/application/medication/use-cases/delete-medication.usecase';
import { FindActiveMedicationByUserUseCase } from 'src/application/medication/use-cases/find-active-medication-by-user.usecase';
import { FindMedicationByIdUseCase } from 'src/application/medication/use-cases/find-medication-by-id.usecase';
import { FindMedicationByUserUseCase } from 'src/application/medication/use-cases/find-medication-by-user.usecase';
import { UpdateMedicationUseCase } from 'src/application/medication/use-cases/update-medication.usecase';
import { CreateMedicationDto } from 'src/interfaces/medication/dtos/create-medication.dto';
import { UpdateMedicationDto } from 'src/interfaces/medication/dtos/update-medication.dto';
export declare class MedicationController {
    private readonly createMedicationUseCase;
    private readonly createMedicationWithAdherenceUseCase;
    private readonly updateMedicationUseCase;
    private readonly deleteMedicationUseCase;
    private readonly getMedicationByIdUseCase;
    private readonly getMedicationsByUserUseCase;
    private readonly getActiveMedicationsByUserUseCase;
    constructor(createMedicationUseCase: CreateMedicationUseCase, createMedicationWithAdherenceUseCase: CreateMedicationWithAdherenceUseCase, updateMedicationUseCase: UpdateMedicationUseCase, deleteMedicationUseCase: DeleteMedicationUseCase, getMedicationByIdUseCase: FindMedicationByIdUseCase, getMedicationsByUserUseCase: FindMedicationByUserUseCase, getActiveMedicationsByUserUseCase: FindActiveMedicationByUserUseCase);
    getAll(userId: string): Promise<{
        id: string;
        user_id: string;
        name: string;
        dosage: {
            amount: number;
            unit: string;
        };
        frequency: {
            times_per_day: number;
            specific_days: string[];
        };
        scheduled_times: string[];
        instructions: string | null | undefined;
        start_date: Date | null | undefined;
        end_date: Date | null | undefined;
        refill_reminder: {
            enabled: boolean;
            threshold: number;
            last_refill?: Date | null;
            next_refill?: Date | null;
            supply_amount: number;
            supply_unit: string;
        } | null | undefined;
        side_effects_to_watch: string[];
        active: boolean;
        medication_type: string | null | undefined;
        image_url: string | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        adherence: any[] | undefined;
        reminders: any[] | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
    }[]>;
    getActive(userId: string): Promise<{
        id: string;
        user_id: string;
        name: string;
        dosage: {
            amount: number;
            unit: string;
        };
        frequency: {
            times_per_day: number;
            specific_days: string[];
        };
        scheduled_times: string[];
        instructions: string | null | undefined;
        start_date: Date | null | undefined;
        end_date: Date | null | undefined;
        refill_reminder: {
            enabled: boolean;
            threshold: number;
            last_refill?: Date | null;
            next_refill?: Date | null;
            supply_amount: number;
            supply_unit: string;
        } | null | undefined;
        side_effects_to_watch: string[];
        active: boolean;
        medication_type: string | null | undefined;
        image_url: string | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        adherence: any[] | undefined;
        reminders: any[] | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
    }[]>;
    get(id: string): Promise<{
        id: string;
        user_id: string;
        name: string;
        dosage: {
            amount: number;
            unit: string;
        };
        frequency: {
            times_per_day: number;
            specific_days: string[];
        };
        scheduled_times: string[];
        instructions: string | null | undefined;
        start_date: Date | null | undefined;
        end_date: Date | null | undefined;
        refill_reminder: {
            enabled: boolean;
            threshold: number;
            last_refill?: Date | null;
            next_refill?: Date | null;
            supply_amount: number;
            supply_unit: string;
        } | null | undefined;
        side_effects_to_watch: string[];
        active: boolean;
        medication_type: string | null | undefined;
        image_url: string | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        adherence: any[] | undefined;
        reminders: any[] | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
    }>;
    create(medication: CreateMedicationDto, userId: string): Promise<{
        id: string;
        user_id: string;
        name: string;
        dosage: {
            amount: number;
            unit: string;
        };
        frequency: {
            times_per_day: number;
            specific_days: string[];
        };
        scheduled_times: string[];
        instructions: string | null | undefined;
        start_date: Date | null | undefined;
        end_date: Date | null | undefined;
        refill_reminder: {
            enabled: boolean;
            threshold: number;
            last_refill?: Date | null;
            next_refill?: Date | null;
            supply_amount: number;
            supply_unit: string;
        } | null | undefined;
        side_effects_to_watch: string[];
        active: boolean;
        medication_type: string | null | undefined;
        image_url: string | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        adherence: any[] | undefined;
        reminders: any[] | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
    }>;
    createSimple(medication: CreateMedicationDto, userId: string): Promise<{
        id: string;
        user_id: string;
        name: string;
        dosage: {
            amount: number;
            unit: string;
        };
        frequency: {
            times_per_day: number;
            specific_days: string[];
        };
        scheduled_times: string[];
        instructions: string | null | undefined;
        start_date: Date | null | undefined;
        end_date: Date | null | undefined;
        refill_reminder: {
            enabled: boolean;
            threshold: number;
            last_refill?: Date | null;
            next_refill?: Date | null;
            supply_amount: number;
            supply_unit: string;
        } | null | undefined;
        side_effects_to_watch: string[];
        active: boolean;
        medication_type: string | null | undefined;
        image_url: string | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        adherence: any[] | undefined;
        reminders: any[] | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
    }>;
    update(id: string, userId: string, medication: UpdateMedicationDto): Promise<{
        id: string;
        user_id: string;
        name: string;
        dosage: {
            amount: number;
            unit: string;
        };
        frequency: {
            times_per_day: number;
            specific_days: string[];
        };
        scheduled_times: string[];
        instructions: string | null | undefined;
        start_date: Date | null | undefined;
        end_date: Date | null | undefined;
        refill_reminder: {
            enabled: boolean;
            threshold: number;
            last_refill?: Date | null;
            next_refill?: Date | null;
            supply_amount: number;
            supply_unit: string;
        } | null | undefined;
        side_effects_to_watch: string[];
        active: boolean;
        medication_type: string | null | undefined;
        image_url: string | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        adherence: any[] | undefined;
        reminders: any[] | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
    }>;
    delete(id: string, userId: string): Promise<{
        message: string;
    }>;
}
