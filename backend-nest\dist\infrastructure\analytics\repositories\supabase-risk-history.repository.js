"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseRiskHistoryRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let SupabaseRiskHistoryRepository = class SupabaseRiskHistoryRepository {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(record) {
        try {
            const existing = await this.prisma.$queryRaw `
        SELECT id FROM user_settings 
        WHERE user_id = ${record.user_id}::uuid
      `;
            if (existing && existing.length > 0) {
                await this.prisma.$executeRaw `
          UPDATE user_settings 
          SET notification_preferences = COALESCE(notification_preferences, '{}'::jsonb) || 
              jsonb_build_object(
                'risk_history', 
                COALESCE(notification_preferences->'risk_history', '[]'::jsonb) || 
                jsonb_build_array(jsonb_build_object(
                  'medication_id', ${record.medication_id},
                  'date', ${record.date},
                  'risk_score', ${record.risk_score}
                ))
              )
          WHERE user_id = ${record.user_id}::uuid
        `;
            }
            else {
                await this.prisma.user_settings.create({
                    data: {
                        user_id: record.user_id,
                        email_enabled: true,
                        preferred_times: ['08:00', '14:00', '20:00'],
                        timezone: 'UTC',
                        notification_preferences: {
                            risk_history: [{
                                    medication_id: record.medication_id,
                                    date: record.date,
                                    risk_score: record.risk_score
                                }]
                        }
                    }
                });
            }
        }
        catch (error) {
            console.error('Error creating risk history record:', error);
            throw error;
        }
    }
    async findByUserMedication(userId, medicationId, startDate, endDate) {
        try {
            const settings = await this.prisma.user_settings.findUnique({
                where: { user_id: userId },
                select: { notification_preferences: true }
            });
            if (!settings?.notification_preferences) {
                return [];
            }
            const riskHistory = settings.notification_preferences?.risk_history || [];
            return riskHistory
                .filter((record) => {
                if (record.medication_id !== medicationId)
                    return false;
                if (startDate && record.date < startDate)
                    return false;
                if (endDate && record.date > endDate)
                    return false;
                return true;
            })
                .map((record) => ({
                user_id: userId,
                medication_id: record.medication_id,
                date: record.date,
                risk_score: record.risk_score
            }));
        }
        catch (error) {
            console.error('Error finding risk history by user medication:', error);
            return [];
        }
    }
    async findByUser(userId, startDate, endDate) {
        try {
            const settings = await this.prisma.user_settings.findUnique({
                where: { user_id: userId },
                select: { notification_preferences: true }
            });
            if (!settings?.notification_preferences) {
                return [];
            }
            const riskHistory = settings.notification_preferences?.risk_history || [];
            return riskHistory
                .filter((record) => {
                if (startDate && record.date < startDate)
                    return false;
                if (endDate && record.date > endDate)
                    return false;
                return true;
            })
                .map((record) => ({
                user_id: userId,
                medication_id: record.medication_id,
                date: record.date,
                risk_score: record.risk_score
            }));
        }
        catch (error) {
            console.error('Error finding risk history by user:', error);
            return [];
        }
    }
    async getLatestRiskScore(userId, medicationId) {
        try {
            const records = await this.findByUserMedication(userId, medicationId);
            if (records.length === 0) {
                return null;
            }
            const latest = records.sort((a, b) => b.date.localeCompare(a.date))[0];
            return latest.risk_score;
        }
        catch (error) {
            console.error('Error getting latest risk score:', error);
            return null;
        }
    }
};
exports.SupabaseRiskHistoryRepository = SupabaseRiskHistoryRepository;
exports.SupabaseRiskHistoryRepository = SupabaseRiskHistoryRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SupabaseRiskHistoryRepository);
//# sourceMappingURL=supabase-risk-history.repository.js.map