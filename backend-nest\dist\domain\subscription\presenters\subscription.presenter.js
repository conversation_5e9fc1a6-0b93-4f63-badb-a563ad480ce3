"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionPresenter = void 0;
class SubscriptionPresenter {
    static present(subscription) {
        return {
            status: subscription.status,
            plan: subscription.plan,
            expiresAt: subscription.expiresAt?.toISOString() || null,
            features: subscription.features.toJson(),
            isActive: subscription.isActive(),
            isPremium: subscription.isPremium(),
        };
    }
    static presentList(subscriptions) {
        return subscriptions.map(subscription => this.present(subscription));
    }
}
exports.SubscriptionPresenter = SubscriptionPresenter;
//# sourceMappingURL=subscription.presenter.js.map