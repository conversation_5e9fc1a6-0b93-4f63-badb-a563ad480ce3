import { Adherence } from '../entities/adherence.entity';
export declare class AdherencePresenter {
    static toHttp(adherence: Adherence): {
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: Date;
        taken_time: Date | null | undefined;
        status: string | null | undefined;
        notes: string | null | undefined;
        reminder_sent: boolean | null | undefined;
        side_effects_reported: string[];
        dosage_taken: {
            amount: number;
            unit: string;
        } | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        medication: import("../../medication/entities/medication.entity").Medication | undefined;
        user: import("../../user/entities/user-aggregate.entity").UserAggregate | undefined;
        reminders: any[] | undefined;
    };
    static toHttpList(adherences: Adherence[]): {
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: Date;
        taken_time: Date | null | undefined;
        status: string | null | undefined;
        notes: string | null | undefined;
        reminder_sent: boolean | null | undefined;
        side_effects_reported: string[];
        dosage_taken: {
            amount: number;
            unit: string;
        } | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        medication: import("../../medication/entities/medication.entity").Medication | undefined;
        user: import("../../user/entities/user-aggregate.entity").UserAggregate | undefined;
        reminders: any[] | undefined;
    }[];
}
