import { Reminder } from '../entities/reminder.entity';
export interface NotificationChannel {
    send(reminder: <PERSON>minder): Promise<boolean>;
}
export interface EmailNotificationData {
    to: string;
    subject: string;
    text: string;
    html: string;
}
export interface SMSNotificationData {
    to: string;
    message: string;
}
export declare abstract class NotificationService {
    abstract sendEmailReminder(reminder: Reminder): Promise<boolean>;
    abstract sendSMSReminder(reminder: Reminder): Promise<boolean>;
    abstract sendWelcomeEmail(email: string, name: string): Promise<boolean>;
    abstract sendSubscriptionConfirmation(email: string, name: string, plan: string): Promise<boolean>;
    abstract sendWeeklyReport(userId: string): Promise<boolean>;
    protected formatDosage(dosage: any): string;
    protected buildEmailReminderData(reminder: Reminder): EmailNotificationData;
    protected buildSMSReminderData(reminder: Reminder): SMSNotificationData;
}
