"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReminderModule = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const reminder_controller_1 = require("./http/controllers/reminder.controller");
const supabase_reminder_repository_1 = require("../../infrastructure/reminder/repositories/supabase-reminder.repository");
const sendgrid_notification_service_1 = require("../../infrastructure/reminder/services/sendgrid-notification.service");
const reminder_scheduler_service_1 = require("../../infrastructure/reminder/services/reminder-scheduler.service");
const reminder_generation_service_1 = require("../../domain/reminder/services/reminder-generation.service");
const create_reminder_usecase_1 = require("../../application/reminder/use-cases/create-reminder.usecase");
const get_upcoming_reminders_usecase_1 = require("../../application/reminder/use-cases/get-upcoming-reminders.usecase");
const get_all_reminders_usecase_1 = require("../../application/reminder/use-cases/get-all-reminders.usecase");
const send_reminder_manually_usecase_1 = require("../../application/reminder/use-cases/send-reminder-manually.usecase");
const delete_reminder_usecase_1 = require("../../application/reminder/use-cases/delete-reminder.usecase");
const update_reminder_settings_usecase_1 = require("../../application/reminder/use-cases/update-reminder-settings.usecase");
const get_user_settings_usecase_1 = require("../../application/reminder/use-cases/get-user-settings.usecase");
const schedule_reminders_usecase_1 = require("../../application/reminder/use-cases/schedule-reminders.usecase");
const prisma_service_1 = require("../../infrastructure/prisma/prisma.service");
const supabase_user_repository_1 = require("../../infrastructure/user/repositories/supabase-user.repository");
const subscription_module_1 = require("../subscription/subscription.module");
let ReminderModule = class ReminderModule {
};
exports.ReminderModule = ReminderModule;
exports.ReminderModule = ReminderModule = __decorate([
    (0, common_1.Module)({
        imports: [schedule_1.ScheduleModule.forRoot(), subscription_module_1.SubscriptionModule],
        controllers: [reminder_controller_1.ReminderController],
        providers: [
            prisma_service_1.PrismaService,
            reminder_generation_service_1.ReminderGenerationService,
            reminder_scheduler_service_1.ReminderSchedulerService,
            create_reminder_usecase_1.CreateReminderUseCase,
            get_upcoming_reminders_usecase_1.GetUpcomingRemindersUseCase,
            get_all_reminders_usecase_1.GetAllRemindersUseCase,
            send_reminder_manually_usecase_1.SendReminderManuallyUseCase,
            delete_reminder_usecase_1.DeleteReminderUseCase,
            update_reminder_settings_usecase_1.UpdateReminderSettingsUseCase,
            get_user_settings_usecase_1.GetUserSettingsUseCase,
            schedule_reminders_usecase_1.ScheduleRemindersUseCase,
            {
                provide: 'ReminderRepository',
                useClass: supabase_reminder_repository_1.SupabaseReminderRepository,
            },
            {
                provide: 'NotificationService',
                useClass: sendgrid_notification_service_1.SendGridNotificationService,
            },
            {
                provide: 'UserRepository',
                useClass: supabase_user_repository_1.SupabaseUserRepository,
            },
        ],
        exports: [
            reminder_generation_service_1.ReminderGenerationService,
            'ReminderRepository',
            'NotificationService',
        ],
    })
], ReminderModule);
//# sourceMappingURL=reminder.module.js.map