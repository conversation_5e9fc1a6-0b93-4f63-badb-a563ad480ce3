"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionDomainService = void 0;
const common_1 = require("@nestjs/common");
const subscription_entity_1 = require("../entities/subscription.entity");
let SubscriptionDomainService = class SubscriptionDomainService {
    calculateExpirationDate(months = 1) {
        const expiresAt = new Date();
        expiresAt.setMonth(expiresAt.getMonth() + months);
        return expiresAt;
    }
    createPremiumSubscription(userId, paymentProviderId, months = 1) {
        const expiresAt = this.calculateExpirationDate(months);
        return new subscription_entity_1.Subscription('', userId, subscription_entity_1.SubscriptionStatus.PREMIUM, subscription_entity_1.SubscriptionPlan.PREMIUM, expiresAt, subscription_entity_1.SubscriptionFeatures.createPremiumFeatures(), paymentProviderId, new Date(), new Date());
    }
    createFreeSubscription(userId) {
        return new subscription_entity_1.Subscription('', userId, subscription_entity_1.SubscriptionStatus.FREE, subscription_entity_1.SubscriptionPlan.FREE, null, subscription_entity_1.SubscriptionFeatures.createFreeFeatures(), null, new Date(), new Date());
    }
    upgradeToPremium(subscription, paymentProviderId, months = 1) {
        const expirationDate = this.calculateExpirationDate(months);
        subscription.updateToPremium(expirationDate, paymentProviderId);
    }
    downgradeToFree(subscription) {
        subscription.downgradeToFree();
    }
    isSubscriptionExpired(subscription) {
        return subscription.isExpired();
    }
    shouldRenewSubscription(subscription, daysBeforeExpiry = 7) {
        if (!subscription.expiresAt)
            return false;
        const renewalDate = new Date();
        renewalDate.setDate(renewalDate.getDate() + daysBeforeExpiry);
        return subscription.expiresAt <= renewalDate;
    }
    validateSubscriptionForFeature(subscription, feature) {
        return subscription.hasFeature(feature);
    }
};
exports.SubscriptionDomainService = SubscriptionDomainService;
exports.SubscriptionDomainService = SubscriptionDomainService = __decorate([
    (0, common_1.Injectable)()
], SubscriptionDomainService);
//# sourceMappingURL=subscription-domain.service.js.map