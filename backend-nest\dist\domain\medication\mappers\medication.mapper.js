"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicationMapper = void 0;
const medication_entity_1 = require("../entities/medication.entity");
class MedicationMapper {
    static toDomain(prismaMedication) {
        return new medication_entity_1.Medication(prismaMedication.id, prismaMedication.user_id, prismaMedication.name, prismaMedication.dosage, prismaMedication.frequency, prismaMedication.scheduled_times, prismaMedication.instructions, prismaMedication.start_date, prismaMedication.end_date, prismaMedication.refill_reminder, prismaMedication.side_effects_to_watch, prismaMedication.active, prismaMedication.medication_type, prismaMedication.image_url, prismaMedication.created_at, prismaMedication.updated_at, prismaMedication.adherence, prismaMedication.reminders, prismaMedication.user);
    }
}
exports.MedicationMapper = MedicationMapper;
//# sourceMappingURL=medication.mapper.js.map