import { Medication } from 'src/domain/medication/entities/medication.entity';
import { MedicationRepository } from 'src/domain/medication/repositories/medication.repository';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { CreateMedicationDto } from '../../../interfaces/medication/dtos/create-medication.dto';
import { UpdateMedicationDto } from '../../../interfaces/medication/dtos/update-medication.dto';
export declare class SupabaseMedicationRepository implements MedicationRepository {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(medication: CreateMedicationDto): Promise<Medication>;
    update(medication: UpdateMedicationDto): Promise<Medication>;
    delete(id: string): Promise<{
        message: string;
    }>;
    findById(id: string): Promise<Medication | null>;
    findByUser(userId: string): Promise<Medication[]>;
    findActiveByUser(userId: string): Promise<Medication[]>;
    findActiveMedicationsByUser(userId: string): Promise<Medication[]>;
}
