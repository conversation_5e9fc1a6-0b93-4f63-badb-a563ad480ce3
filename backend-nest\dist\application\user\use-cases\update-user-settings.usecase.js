"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserSettingsUseCase = void 0;
const common_1 = require("@nestjs/common");
let UpdateUserSettingsUseCase = class UpdateUserSettingsUseCase {
    userRepository;
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async execute(userId, settingsUpdate) {
        const userAggregate = await this.userRepository.getMyProfile(userId);
        if (!userAggregate) {
            throw new common_1.NotFoundException(`User with id ${userId} not found`);
        }
        if (!userAggregate.settings) {
            throw new common_1.NotFoundException(`Settings for user ${userId} not found`);
        }
        const currentSettings = userAggregate.settings;
        const updatedSettings = {
            ...currentSettings,
            ...settingsUpdate,
            updated_at: new Date(),
        };
        const savedSettings = await this.userRepository.updateSettings(userId, updatedSettings);
        return savedSettings;
    }
};
exports.UpdateUserSettingsUseCase = UpdateUserSettingsUseCase;
exports.UpdateUserSettingsUseCase = UpdateUserSettingsUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('UserRepository')),
    __metadata("design:paramtypes", [Object])
], UpdateUserSettingsUseCase);
//# sourceMappingURL=update-user-settings.usecase.js.map