import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getMedications,
  getActiveMedications,
  getMedicationById,
  createMedication,
  createSimpleMedication,
  updateMedication,
  deleteMedication,
  CreateMedicationDto,
} from "../api/medications";
import { toast } from "sonner";

// Get all medications
export const useMedications = () => {
  return useQuery({
    queryKey: ["medications"],
    queryFn: getMedications,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get active medications
export const useActiveMedications = () => {
  return useQuery({
    queryKey: ["medications", "active"],
    queryFn: getActiveMedications,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get medication by ID
export const useMedication = (id: string) => {
  return useQuery({
    queryKey: ["medications", id],
    queryFn: () => getMedicationById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create medication with adherence records
export const useCreateMedication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createMedication,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["medications"] });
      queryClient.invalidateQueries({ queryKey: ["adherence"] });
      toast.success("Medication created successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to create medication");
    },
  });
};

// Create simple medication without adherence records
export const useCreateSimpleMedication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createSimpleMedication,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["medications"] });
      toast.success("Medication created successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to create medication");
    },
  });
};

// Update medication
export const useUpdateMedication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, medication }: { id: string; medication: Partial<CreateMedicationDto> }) =>
      updateMedication(id, medication),
    onSuccess: (data, variables) => {
      queryClient.setQueryData(["medications", variables.id], data);
      queryClient.invalidateQueries({ queryKey: ["medications"] });
      queryClient.invalidateQueries({ queryKey: ["adherence"] });
      toast.success("Medication updated successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to update medication");
    },
  });
};

// Delete medication
export const useDeleteMedication = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteMedication,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["medications"] });
      queryClient.invalidateQueries({ queryKey: ["adherence"] });
      toast.success("Medication deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to delete medication");
    },
  });
};
