"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerModule = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const app_scheduler_service_1 = require("../../infrastructure/scheduler/services/app-scheduler.service");
const process_missed_adherence_usecase_1 = require("../../application/adherence/use-cases/process-missed-adherence.usecase");
const calculate_daily_risk_scores_usecase_1 = require("../../application/analytics/use-cases/calculate-daily-risk-scores.usecase");
const generate_weekly_reports_usecase_1 = require("../../application/reports/use-cases/generate-weekly-reports.usecase");
const supabase_adherence_repository_1 = require("../../infrastructure/adherence/repositories/supabase-adherence.repository");
const supabase_user_repository_1 = require("../../infrastructure/user/repositories/supabase-user.repository");
const supabase_medication_repository_1 = require("../../infrastructure/medication/repositories/supabase-medication.repository");
const supabase_risk_history_repository_1 = require("../../infrastructure/analytics/repositories/supabase-risk-history.repository");
const supabase_subscription_repository_1 = require("../../infrastructure/subscription/repositories/supabase-subscription.repository");
const sendgrid_notification_service_1 = require("../../infrastructure/reminder/services/sendgrid-notification.service");
const prisma_service_1 = require("../../infrastructure/prisma/prisma.service");
let SchedulerModule = class SchedulerModule {
};
exports.SchedulerModule = SchedulerModule;
exports.SchedulerModule = SchedulerModule = __decorate([
    (0, common_1.Module)({
        imports: [schedule_1.ScheduleModule.forRoot()],
        providers: [
            prisma_service_1.PrismaService,
            app_scheduler_service_1.AppSchedulerService,
            process_missed_adherence_usecase_1.ProcessMissedAdherenceUseCase,
            calculate_daily_risk_scores_usecase_1.CalculateDailyRiskScoresUseCase,
            generate_weekly_reports_usecase_1.GenerateWeeklyReportsUseCase,
            {
                provide: 'AdherenceRepository',
                useClass: supabase_adherence_repository_1.SupabaseAdherenceRepository,
            },
            {
                provide: 'UserRepository',
                useClass: supabase_user_repository_1.SupabaseUserRepository,
            },
            {
                provide: 'MedicationRepository',
                useClass: supabase_medication_repository_1.SupabaseMedicationRepository,
            },
            {
                provide: 'RiskHistoryRepository',
                useClass: supabase_risk_history_repository_1.SupabaseRiskHistoryRepository,
            },
            {
                provide: 'SubscriptionRepository',
                useClass: supabase_subscription_repository_1.SupabaseSubscriptionRepository,
            },
            {
                provide: 'NotificationService',
                useClass: sendgrid_notification_service_1.SendGridNotificationService,
            },
        ],
        exports: [app_scheduler_service_1.AppSchedulerService],
    })
], SchedulerModule);
//# sourceMappingURL=scheduler.module.js.map