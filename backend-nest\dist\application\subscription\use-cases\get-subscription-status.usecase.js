"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSubscriptionStatusUseCase = void 0;
const common_1 = require("@nestjs/common");
const subscription_presenter_1 = require("../../../domain/subscription/presenters/subscription.presenter");
const subscription_domain_service_1 = require("../../../domain/subscription/services/subscription-domain.service");
let GetSubscriptionStatusUseCase = class GetSubscriptionStatusUseCase {
    subscriptionRepository;
    subscriptionDomainService;
    constructor(subscriptionRepository, subscriptionDomainService) {
        this.subscriptionRepository = subscriptionRepository;
        this.subscriptionDomainService = subscriptionDomainService;
    }
    async execute(query) {
        const subscription = await this.subscriptionRepository.findByUserId(query.userId);
        if (!subscription) {
            const freeSubscription = this.subscriptionDomainService.createFreeSubscription(query.userId);
            return subscription_presenter_1.SubscriptionPresenter.present(freeSubscription);
        }
        if (this.subscriptionDomainService.isSubscriptionExpired(subscription)) {
            this.subscriptionDomainService.downgradeToFree(subscription);
            await this.subscriptionRepository.update(subscription);
        }
        return subscription_presenter_1.SubscriptionPresenter.present(subscription);
    }
};
exports.GetSubscriptionStatusUseCase = GetSubscriptionStatusUseCase;
exports.GetSubscriptionStatusUseCase = GetSubscriptionStatusUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('SubscriptionRepository')),
    __metadata("design:paramtypes", [Object, subscription_domain_service_1.SubscriptionDomainService])
], GetSubscriptionStatusUseCase);
//# sourceMappingURL=get-subscription-status.usecase.js.map