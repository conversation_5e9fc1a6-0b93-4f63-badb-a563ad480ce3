import { ConfirmDoseUseCase } from 'src/application/adherence/use-cases/confirm-dose.usecase';
import { GetAdherenceHistoryUseCase } from 'src/application/adherence/use-cases/get-adherence-history.usecase';
import { SkipDoseUseCase } from 'src/application/adherence/use-cases/skip-dose.usecase';
import { GetAdherenceStatsUseCase } from 'src/application/adherence/use-cases/get-adherence-stats.usecase';
import { ConfirmDoseDto } from 'src/interfaces/adherence/dtos/confirm-dose.dto';
import { GetAdherenceHistoryDto } from 'src/interfaces/adherence/dtos/get-adherence-history.dto';
import { SkipDoseDto } from 'src/interfaces/adherence/dtos/skip-dose.dto';
import { GetAdherenceStatsDto } from 'src/interfaces/adherence/dtos/get-adherence-stats.dto';
export declare class AdherenceController {
    private readonly getAdherenceHistoryUseCase;
    private readonly confirmDoseUseCase;
    private readonly skipDoseUseCase;
    private readonly getAdherenceStatsUseCase;
    constructor(getAdherenceHistoryUseCase: GetAdherenceHistoryUseCase, confirmDoseUseCase: ConfirmDoseUseCase, skipDoseUseCase: SkipDoseUseCase, getAdherenceStatsUseCase: GetAdherenceStatsUseCase);
    getHistory(query: GetAdherenceHistoryDto, userId: string): Promise<{
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: Date;
        taken_time: Date | null | undefined;
        status: string | null | undefined;
        notes: string | null | undefined;
        reminder_sent: boolean | null | undefined;
        side_effects_reported: string[];
        dosage_taken: {
            amount: number;
            unit: string;
        } | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        medication: import("../../../../domain/medication/entities/medication.entity").Medication | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
        reminders: any[] | undefined;
    }[]>;
    confirmDose(body: ConfirmDoseDto, userId: string): Promise<{
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: Date;
        taken_time: Date | null | undefined;
        status: string | null | undefined;
        notes: string | null | undefined;
        reminder_sent: boolean | null | undefined;
        side_effects_reported: string[];
        dosage_taken: {
            amount: number;
            unit: string;
        } | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        medication: import("../../../../domain/medication/entities/medication.entity").Medication | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
        reminders: any[] | undefined;
    }>;
    skipDose(body: SkipDoseDto, userId: string): Promise<{
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: Date;
        taken_time: Date | null | undefined;
        status: string | null | undefined;
        notes: string | null | undefined;
        reminder_sent: boolean | null | undefined;
        side_effects_reported: string[];
        dosage_taken: {
            amount: number;
            unit: string;
        } | null | undefined;
        created_at: Date | null | undefined;
        updated_at: Date | null | undefined;
        medication: import("../../../../domain/medication/entities/medication.entity").Medication | undefined;
        user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
        reminders: any[] | undefined;
    }>;
    getStats(query: GetAdherenceStatsDto, userId: string): Promise<import("../../../../domain/adherence/entities/adherence-stats.entity").AdherenceStats>;
}
