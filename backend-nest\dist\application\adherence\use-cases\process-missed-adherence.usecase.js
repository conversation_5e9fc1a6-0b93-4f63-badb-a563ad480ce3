"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessMissedAdherenceUseCase = void 0;
const common_1 = require("@nestjs/common");
let ProcessMissedAdherenceUseCase = class ProcessMissedAdherenceUseCase {
    adherenceRepository;
    constructor(adherenceRepository) {
        this.adherenceRepository = adherenceRepository;
    }
    async execute() {
        const now = new Date();
        const todayStr = now.toISOString().split('T')[0];
        const cutoffTime = new Date(now.getTime() - 2 * 60 * 60 * 1000);
        let processed = 0;
        let updated = 0;
        let failed = 0;
        try {
            const allMissedRecords = await this.adherenceRepository.findPendingForMissedProcessing(todayStr, cutoffTime);
            processed = allMissedRecords.length;
            for (const record of allMissedRecords) {
                try {
                    await this.adherenceRepository.updateStatus(record.id, 'missed');
                    updated++;
                }
                catch (error) {
                    console.error(`Error updating adherence record ${record.id}:`, error);
                    failed++;
                }
            }
            console.log(`Processed missed adherence: ${processed} total, ${updated} updated, ${failed} failed`);
            return { processed, updated, failed };
        }
        catch (error) {
            console.error('Error in process missed adherence:', error);
            throw error;
        }
    }
};
exports.ProcessMissedAdherenceUseCase = ProcessMissedAdherenceUseCase;
exports.ProcessMissedAdherenceUseCase = ProcessMissedAdherenceUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('AdherenceRepository')),
    __metadata("design:paramtypes", [Object])
], ProcessMissedAdherenceUseCase);
//# sourceMappingURL=process-missed-adherence.usecase.js.map