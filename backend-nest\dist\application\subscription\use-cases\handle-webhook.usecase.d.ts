import { PaymentProvider, PaymentProviderType } from '../../../domain/subscription/services/payment-provider.interface';
import { UpdateSubscriptionStatusUseCase } from './update-subscription-status.usecase';
export interface HandleWebhookCommand {
    paymentProvider: PaymentProviderType;
    payload: any;
    signature?: string;
}
export declare class HandleWebhookUseCase {
    private readonly stripeProvider;
    private readonly mercadoPagoProvider;
    private readonly updateSubscriptionStatusUseCase;
    constructor(stripeProvider: PaymentProvider, mercadoPagoProvider: PaymentProvider, updateSubscriptionStatusUseCase: UpdateSubscriptionStatusUseCase);
    execute(command: HandleWebhookCommand): Promise<{
        received: boolean;
    }>;
    private getPaymentProvider;
}
