"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicationModule = void 0;
const common_1 = require("@nestjs/common");
const medication_controller_1 = require("./http/controllers/medication.controller");
const supabase_medication_repository_1 = require("../../infrastructure/medication/repositories/supabase-medication.repository");
const create_medication_usecase_1 = require("../../application/medication/use-cases/create-medication.usecase");
const create_medication_with_adherence_usecase_1 = require("../../application/medication/use-cases/create-medication-with-adherence.usecase");
const update_medication_usecase_1 = require("../../application/medication/use-cases/update-medication.usecase");
const delete_medication_usecase_1 = require("../../application/medication/use-cases/delete-medication.usecase");
const find_medication_by_id_usecase_1 = require("../../application/medication/use-cases/find-medication-by-id.usecase");
const find_medication_by_user_usecase_1 = require("../../application/medication/use-cases/find-medication-by-user.usecase");
const find_active_medication_by_user_usecase_1 = require("../../application/medication/use-cases/find-active-medication-by-user.usecase");
const prisma_service_1 = require("../../infrastructure/prisma/prisma.service");
const supabase_adherence_repository_1 = require("../../infrastructure/adherence/repositories/supabase-adherence.repository");
const adherence_generation_service_1 = require("../../domain/adherence/services/adherence-generation.service");
const date_calculation_service_1 = require("../../domain/adherence/services/date-calculation.service");
const create_reminders_for_medication_usecase_1 = require("../../application/reminder/use-cases/create-reminders-for-medication.usecase");
const supabase_reminder_repository_1 = require("../../infrastructure/reminder/repositories/supabase-reminder.repository");
const reminder_generation_service_1 = require("../../domain/reminder/services/reminder-generation.service");
let MedicationModule = class MedicationModule {
};
exports.MedicationModule = MedicationModule;
exports.MedicationModule = MedicationModule = __decorate([
    (0, common_1.Module)({
        controllers: [medication_controller_1.MedicationController],
        providers: [
            prisma_service_1.PrismaService,
            create_medication_usecase_1.CreateMedicationUseCase,
            create_medication_with_adherence_usecase_1.CreateMedicationWithAdherenceUseCase,
            update_medication_usecase_1.UpdateMedicationUseCase,
            delete_medication_usecase_1.DeleteMedicationUseCase,
            find_medication_by_id_usecase_1.FindMedicationByIdUseCase,
            find_medication_by_user_usecase_1.FindMedicationByUserUseCase,
            find_active_medication_by_user_usecase_1.FindActiveMedicationByUserUseCase,
            adherence_generation_service_1.AdherenceGenerationService,
            date_calculation_service_1.DateCalculationService,
            create_reminders_for_medication_usecase_1.CreateRemindersForMedicationUseCase,
            reminder_generation_service_1.ReminderGenerationService,
            {
                provide: 'MedicationRepository',
                useClass: supabase_medication_repository_1.SupabaseMedicationRepository,
            },
            {
                provide: 'AdherenceRepository',
                useClass: supabase_adherence_repository_1.SupabaseAdherenceRepository,
            },
            {
                provide: 'ReminderRepository',
                useClass: supabase_reminder_repository_1.SupabaseReminderRepository,
            },
        ],
    })
], MedicationModule);
//# sourceMappingURL=medication.module.js.map