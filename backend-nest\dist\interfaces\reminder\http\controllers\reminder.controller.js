"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReminderController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../../common/guards/jwt-auth.guard");
const get_user_id_decorator_1 = require("../../../common/decorators/get-user-id.decorator");
const create_reminder_usecase_1 = require("../../../../application/reminder/use-cases/create-reminder.usecase");
const get_upcoming_reminders_usecase_1 = require("../../../../application/reminder/use-cases/get-upcoming-reminders.usecase");
const get_all_reminders_usecase_1 = require("../../../../application/reminder/use-cases/get-all-reminders.usecase");
const send_reminder_manually_usecase_1 = require("../../../../application/reminder/use-cases/send-reminder-manually.usecase");
const delete_reminder_usecase_1 = require("../../../../application/reminder/use-cases/delete-reminder.usecase");
const update_reminder_settings_usecase_1 = require("../../../../application/reminder/use-cases/update-reminder-settings.usecase");
const get_user_settings_usecase_1 = require("../../../../application/reminder/use-cases/get-user-settings.usecase");
const create_reminder_dto_1 = require("../../dtos/create-reminder.dto");
const update_reminder_settings_dto_1 = require("../../dtos/update-reminder-settings.dto");
const reminder_presenter_1 = require("../../../../domain/reminder/presenters/reminder.presenter");
const subscription_guard_1 = require("../../../common/guards/subscription.guard");
let ReminderController = class ReminderController {
    createReminderUseCase;
    getUpcomingRemindersUseCase;
    getAllRemindersUseCase;
    sendReminderManuallyUseCase;
    deleteReminderUseCase;
    updateReminderSettingsUseCase;
    getUserSettingsUseCase;
    constructor(createReminderUseCase, getUpcomingRemindersUseCase, getAllRemindersUseCase, sendReminderManuallyUseCase, deleteReminderUseCase, updateReminderSettingsUseCase, getUserSettingsUseCase) {
        this.createReminderUseCase = createReminderUseCase;
        this.getUpcomingRemindersUseCase = getUpcomingRemindersUseCase;
        this.getAllRemindersUseCase = getAllRemindersUseCase;
        this.sendReminderManuallyUseCase = sendReminderManuallyUseCase;
        this.deleteReminderUseCase = deleteReminderUseCase;
        this.updateReminderSettingsUseCase = updateReminderSettingsUseCase;
        this.getUserSettingsUseCase = getUserSettingsUseCase;
    }
    async getAllReminders(userId, startDate, endDate) {
        const reminders = await this.getAllRemindersUseCase.execute(userId, startDate, endDate);
        return reminder_presenter_1.ReminderPresenter.toHttpList(reminders);
    }
    async getUpcomingReminders(userId, limit) {
        const limitNumber = limit ? parseInt(limit, 10) : 10;
        const reminders = await this.getUpcomingRemindersUseCase.execute(userId, limitNumber);
        return reminder_presenter_1.ReminderPresenter.toHttpList(reminders);
    }
    async createReminder(reminder, userId) {
        const created = await this.createReminderUseCase.execute({
            ...reminder,
            user_id: userId,
        });
        return reminder_presenter_1.ReminderPresenter.toHttp(created);
    }
    async sendReminderManually(id, userId) {
        return this.sendReminderManuallyUseCase.execute(id, userId);
    }
    async deleteReminder(id, userId) {
        return this.deleteReminderUseCase.execute(id, userId);
    }
    async getUserSettings(userId) {
        return this.getUserSettingsUseCase.execute(userId);
    }
    async updateReminderSettings(settings, userId) {
        return this.updateReminderSettingsUseCase.execute(userId, settings);
    }
};
exports.ReminderController = ReminderController;
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], ReminderController.prototype, "getAllReminders", null);
__decorate([
    (0, common_1.Get)('upcoming'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, subscription_guard_1.SubscriptionGuard),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ReminderController.prototype, "getUpcomingReminders", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, subscription_guard_1.SubscriptionGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_reminder_dto_1.CreateReminderDto, String]),
    __metadata("design:returntype", Promise)
], ReminderController.prototype, "createReminder", null);
__decorate([
    (0, common_1.Post)(':id/send'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, subscription_guard_1.SubscriptionGuard),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ReminderController.prototype, "sendReminderManually", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ReminderController.prototype, "deleteReminder", null);
__decorate([
    (0, common_1.Get)('settings'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ReminderController.prototype, "getUserSettings", null);
__decorate([
    (0, common_1.Put)('settings'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_reminder_settings_dto_1.UpdateReminderSettingsDto, String]),
    __metadata("design:returntype", Promise)
], ReminderController.prototype, "updateReminderSettings", null);
exports.ReminderController = ReminderController = __decorate([
    (0, common_1.Controller)('reminders'),
    __metadata("design:paramtypes", [create_reminder_usecase_1.CreateReminderUseCase,
        get_upcoming_reminders_usecase_1.GetUpcomingRemindersUseCase,
        get_all_reminders_usecase_1.GetAllRemindersUseCase,
        send_reminder_manually_usecase_1.SendReminderManuallyUseCase,
        delete_reminder_usecase_1.DeleteReminderUseCase,
        update_reminder_settings_usecase_1.UpdateReminderSettingsUseCase,
        get_user_settings_usecase_1.GetUserSettingsUseCase])
], ReminderController);
//# sourceMappingURL=reminder.controller.js.map