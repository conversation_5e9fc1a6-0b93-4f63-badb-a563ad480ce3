"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionMapper = void 0;
const subscription_entity_1 = require("../entities/subscription.entity");
class SubscriptionMapper {
    static fromDatabase(data) {
        return new subscription_entity_1.Subscription(data.id, data.user_id || data.userId, data.subscription_status, data.subscription_plan, data.subscription_expires_at ? new Date(data.subscription_expires_at) : null, data.subscription_features
            ? subscription_entity_1.SubscriptionFeatures.fromJson(data.subscription_features)
            : subscription_entity_1.SubscriptionFeatures.createFreeFeatures(), data.payment_provider_id || null, data.created_at ? new Date(data.created_at) : undefined, data.updated_at ? new Date(data.updated_at) : undefined);
    }
    static toDatabase(subscription) {
        return {
            id: subscription.id,
            user_id: subscription.userId,
            subscription_status: subscription.status,
            subscription_plan: subscription.plan,
            subscription_expires_at: subscription.expiresAt?.toISOString() || null,
            subscription_features: subscription.features.toJson(),
            payment_provider_id: subscription.paymentProviderId,
            created_at: subscription.createdAt?.toISOString(),
            updated_at: subscription.updatedAt?.toISOString(),
        };
    }
    static fromUserData(userData) {
        return new subscription_entity_1.Subscription(userData.id, userData.id, userData.subscription_status || subscription_entity_1.SubscriptionStatus.FREE, userData.subscription_plan || subscription_entity_1.SubscriptionPlan.FREE, userData.subscription_expires_at ? new Date(userData.subscription_expires_at) : null, userData.subscription_features
            ? subscription_entity_1.SubscriptionFeatures.fromJson(userData.subscription_features)
            : subscription_entity_1.SubscriptionFeatures.createFreeFeatures(), userData.payment_provider_id || null, userData.created_at ? new Date(userData.created_at) : undefined, userData.updated_at ? new Date(userData.updated_at) : undefined);
    }
}
exports.SubscriptionMapper = SubscriptionMapper;
//# sourceMappingURL=subscription.mapper.js.map