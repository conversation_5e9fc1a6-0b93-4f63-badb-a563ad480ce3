"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserMapper = void 0;
const user_entity_1 = require("../entities/user.entity");
const user_settings_entity_1 = require("../entities/user-settings.entity");
const user_aggregate_entity_1 = require("../entities/user-aggregate.entity");
class UserMapper {
    static toDomain(prismaUser, authUserId, prismaSettings) {
        const user = new user_entity_1.User(prismaUser.id, prismaUser.auth_user_id, prismaUser.name, prismaUser.email, prismaUser.password, prismaUser.date_of_birth, prismaUser.gender, prismaUser.allergies, prismaUser.conditions, prismaUser.is_admin, prismaUser.phone_number, prismaUser.emergency_contact, prismaUser.created_at, prismaUser.updated_at, prismaUser.subscription_status, prismaUser.subscription_plan, prismaUser.subscription_expires_at, prismaUser.subscription_features);
        const settings = prismaSettings
            ? new user_settings_entity_1.UserSettings(prismaSettings.id, prismaSettings.user_id, prismaSettings.email_enabled, prismaSettings.preferred_times, prismaSettings.timezone, prismaSettings.notification_preferences, prismaSettings.created_at, prismaSettings.updated_at)
            : null;
        return new user_aggregate_entity_1.UserAggregate(prismaUser.id, authUserId, user, settings);
    }
}
exports.UserMapper = UserMapper;
//# sourceMappingURL=user.mapper.js.map