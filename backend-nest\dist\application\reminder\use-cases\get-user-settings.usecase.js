"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetUserSettingsUseCase = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../../infrastructure/prisma/prisma.service");
let GetUserSettingsUseCase = class GetUserSettingsUseCase {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async execute(userId) {
        const settings = await this.prisma.user_settings.findUnique({
            where: { user_id: userId },
            select: {
                email_enabled: true,
                preferred_times: true,
                timezone: true,
                notification_preferences: true,
            },
        });
        if (!settings) {
            return {
                emailEnabled: true,
                preferredTimes: ['08:00', '14:00', '20:00'],
                timezone: 'UTC',
                notificationPreferences: {
                    email: true,
                    push: false,
                    sms: false,
                },
            };
        }
        return {
            emailEnabled: settings.email_enabled,
            preferredTimes: settings.preferred_times,
            timezone: settings.timezone,
            notificationPreferences: settings.notification_preferences,
        };
    }
};
exports.GetUserSettingsUseCase = GetUserSettingsUseCase;
exports.GetUserSettingsUseCase = GetUserSettingsUseCase = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], GetUserSettingsUseCase);
//# sourceMappingURL=get-user-settings.usecase.js.map