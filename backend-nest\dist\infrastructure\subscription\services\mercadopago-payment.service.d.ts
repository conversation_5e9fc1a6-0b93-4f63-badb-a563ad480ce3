import { ConfigService } from '@nestjs/config';
import { PaymentProvider, CreateCheckoutSessionRequest, CheckoutSessionResponse, WebhookResult, PaymentDetails } from '../../../domain/subscription/services/payment-provider.interface';
export declare class MercadoPagoPaymentService implements PaymentProvider {
    private readonly configService;
    private client;
    constructor(configService: ConfigService);
    createCheckoutSession(request: CreateCheckoutSessionRequest): Promise<CheckoutSessionResponse>;
    handleWebhook(payload: any, signature?: string): Promise<WebhookResult>;
    getPaymentDetails(paymentId: string): Promise<PaymentDetails>;
}
