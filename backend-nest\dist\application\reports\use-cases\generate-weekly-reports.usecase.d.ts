import { UserRepository } from '../../../domain/user/repositories/user.repository';
import { NotificationService } from '../../../domain/reminder/services/notification.service';
import { SubscriptionRepository } from '../../../domain/subscription/repositories/subscription.repository';
export interface WeeklyReportsResult {
    usersProcessed: number;
    reportsGenerated: number;
    reportsSent: number;
    errors: number;
}
export declare class GenerateWeeklyReportsUseCase {
    private readonly userRepository;
    private readonly notificationService;
    private readonly subscriptionRepository;
    constructor(userRepository: UserRepository, notificationService: NotificationService, subscriptionRepository: SubscriptionRepository);
    execute(): Promise<WeeklyReportsResult>;
}
