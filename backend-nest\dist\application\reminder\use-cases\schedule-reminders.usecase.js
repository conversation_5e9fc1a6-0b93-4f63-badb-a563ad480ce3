"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ScheduleRemindersUseCase_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduleRemindersUseCase = void 0;
const common_1 = require("@nestjs/common");
const notification_service_1 = require("../../../domain/reminder/services/notification.service");
let ScheduleRemindersUseCase = ScheduleRemindersUseCase_1 = class ScheduleRemindersUseCase {
    reminderRepository;
    notificationService;
    userRepository;
    logger = new common_1.Logger(ScheduleRemindersUseCase_1.name);
    constructor(reminderRepository, notificationService, userRepository) {
        this.reminderRepository = reminderRepository;
        this.notificationService = notificationService;
        this.userRepository = userRepository;
    }
    async execute() {
        const now = new Date();
        const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60000);
        const today = now.toISOString().split('T')[0];
        const currentTime = now.toTimeString().slice(0, 5);
        const futureTime = fiveMinutesFromNow.toTimeString().slice(0, 5);
        this.logger.log(`Checking reminders for ${today} between ${currentTime} and ${futureTime}`);
        const reminders = await this.reminderRepository.findPendingReminders(undefined, today, currentTime, futureTime);
        let processed = 0;
        let sent = 0;
        let failed = 0;
        for (const reminder of reminders) {
            try {
                processed++;
                const user = await this.userRepository.findById(reminder.user_id);
                if (!user) {
                    this.logger.warn(`User ${reminder.user_id} not found, skipping reminder`);
                    continue;
                }
                const isPremium = user.subscription_status === 'premium' &&
                    user.subscription_expires_at &&
                    new Date(user.subscription_expires_at) > new Date();
                if (!isPremium) {
                    this.logger.log(`User ${reminder.user_id} is not premium, skipping reminder`);
                    continue;
                }
                await this.notificationService.sendEmailReminder(reminder);
                await this.reminderRepository.markAsSent(reminder.id, 'email');
                sent++;
                this.logger.log(`Reminder sent successfully for user ${reminder.user_id}, medication ${reminder.medication?.name}`);
            }
            catch (error) {
                failed++;
                this.logger.error(`Error processing reminder ${reminder.id}:`, error);
                try {
                    await this.reminderRepository.markAsFailed(reminder.id);
                }
                catch (updateError) {
                    this.logger.error(`Error marking reminder as failed:`, updateError);
                }
            }
        }
        this.logger.log(`Reminder processing complete: ${processed} processed, ${sent} sent, ${failed} failed`);
        return { processed, sent, failed };
    }
};
exports.ScheduleRemindersUseCase = ScheduleRemindersUseCase;
exports.ScheduleRemindersUseCase = ScheduleRemindersUseCase = ScheduleRemindersUseCase_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('ReminderRepository')),
    __param(1, (0, common_1.Inject)('NotificationService')),
    __param(2, (0, common_1.Inject)('UserRepository')),
    __metadata("design:paramtypes", [Object, notification_service_1.NotificationService, Object])
], ScheduleRemindersUseCase);
//# sourceMappingURL=schedule-reminders.usecase.js.map