"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HandleWebhookUseCase = void 0;
const common_1 = require("@nestjs/common");
const payment_provider_interface_1 = require("../../../domain/subscription/services/payment-provider.interface");
const update_subscription_status_usecase_1 = require("./update-subscription-status.usecase");
const subscription_entity_1 = require("../../../domain/subscription/entities/subscription.entity");
let HandleWebhookUseCase = class HandleWebhookUseCase {
    stripeProvider;
    mercadoPagoProvider;
    updateSubscriptionStatusUseCase;
    constructor(stripeProvider, mercadoPagoProvider, updateSubscriptionStatusUseCase) {
        this.stripeProvider = stripeProvider;
        this.mercadoPagoProvider = mercadoPagoProvider;
        this.updateSubscriptionStatusUseCase = updateSubscriptionStatusUseCase;
    }
    async execute(command) {
        try {
            const provider = this.getPaymentProvider(command.paymentProvider);
            const result = await provider.handleWebhook(command.payload, command.signature);
            if (result.success && result.shouldUpdateSubscription && result.userId) {
                console.log('Payment approved, updating subscription for user:', result.userId);
                try {
                    await this.updateSubscriptionStatusUseCase.execute({
                        userId: result.userId,
                        subscriptionId: result.paymentId,
                        status: subscription_entity_1.SubscriptionStatus.PREMIUM,
                    });
                    console.log('Subscription updated successfully');
                }
                catch (updateError) {
                    console.error('Error updating subscription:', updateError);
                }
            }
            return { received: true };
        }
        catch (error) {
            console.error('Error handling webhook:', error);
            return { received: true };
        }
    }
    getPaymentProvider(type) {
        switch (type) {
            case payment_provider_interface_1.PaymentProviderType.STRIPE:
                return this.stripeProvider;
            case payment_provider_interface_1.PaymentProviderType.MERCADOPAGO:
                return this.mercadoPagoProvider;
            default:
                throw new Error(`Unsupported payment provider: ${type}`);
        }
    }
};
exports.HandleWebhookUseCase = HandleWebhookUseCase;
exports.HandleWebhookUseCase = HandleWebhookUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('StripePaymentProvider')),
    __param(1, (0, common_1.Inject)('MercadoPagoPaymentProvider')),
    __metadata("design:paramtypes", [Object, Object, update_subscription_status_usecase_1.UpdateSubscriptionStatusUseCase])
], HandleWebhookUseCase);
//# sourceMappingURL=handle-webhook.usecase.js.map