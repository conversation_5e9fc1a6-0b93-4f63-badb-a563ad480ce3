import { supabase } from "../config/supabase";
import { RealtimeChannel } from "@supabase/supabase-js";

export type RealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE';

export interface RealtimeSubscriptionConfig {
  table: string;
  event?: RealtimeEvent | '*';
  filter?: string;
  onInsert?: (payload: any) => void;
  onUpdate?: (payload: any) => void;
  onDelete?: (payload: any) => void;
  onChange?: (payload: any) => void;
}

class RealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map();

  // Subscribe to real-time changes for a specific table
  subscribe(config: RealtimeSubscriptionConfig): string {
    const channelId = `${config.table}_${Date.now()}_${Math.random()}`;
    
    let channel = supabase.channel(channelId);

    // Configure the subscription based on the config
    if (config.filter) {
      channel = channel.on(
        'postgres_changes',
        {
          event: config.event || '*',
          schema: 'public',
          table: config.table,
          filter: config.filter,
        },
        (payload) => {
          this.handleRealtimeEvent(payload, config);
        }
      );
    } else {
      channel = channel.on(
        'postgres_changes',
        {
          event: config.event || '*',
          schema: 'public',
          table: config.table,
        },
        (payload) => {
          this.handleRealtimeEvent(payload, config);
        }
      );
    }

    // Subscribe to the channel
    channel.subscribe((status) => {
      console.log(`Realtime subscription status for ${config.table}:`, status);
    });

    this.channels.set(channelId, channel);
    return channelId;
  }

  // Unsubscribe from a specific channel
  unsubscribe(channelId: string): void {
    const channel = this.channels.get(channelId);
    if (channel) {
      supabase.removeChannel(channel);
      this.channels.delete(channelId);
    }
  }

  // Unsubscribe from all channels
  unsubscribeAll(): void {
    this.channels.forEach((channel) => {
      supabase.removeChannel(channel);
    });
    this.channels.clear();
  }

  // Handle real-time events
  private handleRealtimeEvent(payload: any, config: RealtimeSubscriptionConfig): void {
    const { eventType, new: newRecord, old: oldRecord } = payload;

    // Call the appropriate handler based on event type
    switch (eventType) {
      case 'INSERT':
        config.onInsert?.(newRecord);
        break;
      case 'UPDATE':
        config.onUpdate?.({ new: newRecord, old: oldRecord });
        break;
      case 'DELETE':
        config.onDelete?.(oldRecord);
        break;
    }

    // Always call the general onChange handler
    config.onChange?.(payload);
  }

  // Get the current user ID for filtering
  async getCurrentUserId(): Promise<string | null> {
    const { data: { user } } = await supabase.auth.getUser();
    return user?.id || null;
  }
}

export const realtimeService = new RealtimeService();
