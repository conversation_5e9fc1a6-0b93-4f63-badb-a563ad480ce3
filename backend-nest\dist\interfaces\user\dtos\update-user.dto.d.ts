declare class EmergencyContactDto {
    name: string;
    phone_number: string;
    relationship: string;
}
declare class SubscriptionFeaturesDto {
    custom_reminders: boolean;
    custom_notifications: boolean;
    risk_analytics: boolean;
}
export declare class UpdateUserDto {
    name?: string | null;
    email?: string | null;
    password?: string | null;
    date_of_birth?: string | null;
    gender?: string | null;
    allergies?: string[] | null;
    conditions?: string[] | null;
    is_admin?: boolean;
    phone_number?: string | null;
    emergency_contact?: EmergencyContactDto | null;
    subscription_status?: string | null;
    subscription_plan?: string | null;
    subscription_expires_at?: string | null;
    subscription_features?: SubscriptionFeaturesDto | null;
}
export {};
