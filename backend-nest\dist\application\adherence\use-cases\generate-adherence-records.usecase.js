"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateAdherenceRecordsUseCase = void 0;
const common_1 = require("@nestjs/common");
const adherence_generation_service_1 = require("../../../domain/adherence/services/adherence-generation.service");
let GenerateAdherenceRecordsUseCase = class GenerateAdherenceRecordsUseCase {
    adherenceRepository;
    medicationRepository;
    userRepository;
    adherenceGenerationService;
    constructor(adherenceRepository, medicationRepository, userRepository, adherenceGenerationService) {
        this.adherenceRepository = adherenceRepository;
        this.medicationRepository = medicationRepository;
        this.userRepository = userRepository;
        this.adherenceGenerationService = adherenceGenerationService;
    }
    async execute() {
        let usersProcessed = 0;
        let medicationsProcessed = 0;
        let recordsGenerated = 0;
        let errors = 0;
        try {
            const users = await this.userRepository.findAll();
            for (const user of users) {
                try {
                    usersProcessed++;
                    const medications = await this.medicationRepository.findActiveMedicationsByUser(user.id);
                    for (const medication of medications) {
                        try {
                            medicationsProcessed++;
                            const generated = await this.generateAdherenceForMedication(medication.id, user.id);
                            recordsGenerated += generated;
                        }
                        catch (medError) {
                            console.error(`Error generating adherence for med ${medication.id} user ${user.id}:`, medError);
                            errors++;
                        }
                    }
                }
                catch (userError) {
                    console.error(`Error processing user ${user.id}:`, userError);
                    errors++;
                }
            }
            console.log(`Daily adherence generation completed: ${usersProcessed} users, ${medicationsProcessed} medications, ${recordsGenerated} records generated, ${errors} errors`);
            return { usersProcessed, medicationsProcessed, recordsGenerated, errors };
        }
        catch (error) {
            console.error('Error in daily adherence generation:', error);
            throw error;
        }
    }
    async generateAdherenceForMedication(medicationId, userId) {
        try {
            const medication = await this.medicationRepository.findById(medicationId);
            if (!medication) {
                throw new Error('Medication not found');
            }
            const today = new Date();
            const endDate = new Date(today);
            endDate.setDate(endDate.getDate() + 7);
            let recordsGenerated = 0;
            for (let date = new Date(today); date <= endDate; date.setDate(date.getDate() + 1)) {
                if (this.shouldTakeMedicationOnDay(medication, date)) {
                    for (const timeStr of medication.scheduled_times || []) {
                        const [hours, minutes] = timeStr.split(':').map(Number);
                        const scheduledDateTime = new Date(date);
                        scheduledDateTime.setHours(hours, minutes, 0, 0);
                        const now = new Date();
                        if (date.getDate() === today.getDate() && scheduledDateTime < now) {
                            continue;
                        }
                        const existing = await this.adherenceRepository.findByUserMedicationDateTime(userId, medicationId, date.toISOString().split('T')[0], timeStr);
                        if (!existing) {
                            await this.adherenceRepository.create({
                                user_id: userId,
                                medication_id: medicationId,
                                scheduled_time: timeStr,
                                scheduled_date: date.toISOString().split('T')[0],
                                status: 'pending'
                            });
                            recordsGenerated++;
                        }
                    }
                }
            }
            return recordsGenerated;
        }
        catch (error) {
            console.error('Error generating adherence records:', error);
            throw error;
        }
    }
    shouldTakeMedicationOnDay(medication, date) {
        if (!medication.active)
            return false;
        if (medication.start_date && new Date(medication.start_date) > date)
            return false;
        if (medication.end_date && new Date(medication.end_date) < date)
            return false;
        const frequency = medication.frequency;
        if (!frequency)
            return true;
        if (frequency.type === 'daily') {
            return true;
        }
        else if (frequency.type === 'specific_days' && frequency.days) {
            const dayOfWeek = date.getDay();
            return frequency.days.includes(dayOfWeek);
        }
        else if (frequency.type === 'interval' && frequency.interval) {
            const startDate = new Date(medication.start_date || medication.created_at);
            const daysDiff = Math.floor((date.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            return daysDiff % frequency.interval === 0;
        }
        return true;
    }
};
exports.GenerateAdherenceRecordsUseCase = GenerateAdherenceRecordsUseCase;
exports.GenerateAdherenceRecordsUseCase = GenerateAdherenceRecordsUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('AdherenceRepository')),
    __param(1, (0, common_1.Inject)('MedicationRepository')),
    __param(2, (0, common_1.Inject)('UserRepository')),
    __metadata("design:paramtypes", [Object, Object, Object, adherence_generation_service_1.AdherenceGenerationService])
], GenerateAdherenceRecordsUseCase);
//# sourceMappingURL=generate-adherence-records.usecase.js.map