"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCheckoutSessionUseCase = void 0;
const common_1 = require("@nestjs/common");
const payment_provider_interface_1 = require("../../../domain/subscription/services/payment-provider.interface");
let CreateCheckoutSessionUseCase = class CreateCheckoutSessionUseCase {
    stripeProvider;
    mercadoPagoProvider;
    constructor(stripeProvider, mercadoPagoProvider) {
        this.stripeProvider = stripeProvider;
        this.mercadoPagoProvider = mercadoPagoProvider;
    }
    async execute(command) {
        if (!command.userId) {
            throw new common_1.UnauthorizedException('User not authenticated');
        }
        if (!command.priceId || !command.paymentProvider) {
            throw new common_1.BadRequestException('Price ID and payment provider are required');
        }
        const provider = this.getPaymentProvider(command.paymentProvider);
        const request = {
            userId: command.userId,
            email: command.email,
            priceId: command.priceId,
            currency: command.currency || 'ARS',
        };
        try {
            return await provider.createCheckoutSession(request);
        }
        catch (error) {
            throw new common_1.BadRequestException(`Error creating checkout session: ${error.message}`);
        }
    }
    getPaymentProvider(type) {
        switch (type) {
            case payment_provider_interface_1.PaymentProviderType.STRIPE:
                return this.stripeProvider;
            case payment_provider_interface_1.PaymentProviderType.MERCADOPAGO:
                return this.mercadoPagoProvider;
            default:
                throw new common_1.BadRequestException(`Unsupported payment provider: ${type}`);
        }
    }
};
exports.CreateCheckoutSessionUseCase = CreateCheckoutSessionUseCase;
exports.CreateCheckoutSessionUseCase = CreateCheckoutSessionUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('StripePaymentProvider')),
    __param(1, (0, common_1.Inject)('MercadoPagoPaymentProvider')),
    __metadata("design:paramtypes", [Object, Object])
], CreateCheckoutSessionUseCase);
//# sourceMappingURL=create-checkout-session.usecase.js.map