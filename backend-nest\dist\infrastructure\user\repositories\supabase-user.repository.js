"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseUserRepository = void 0;
const common_1 = require("@nestjs/common");
const user_settings_entity_1 = require("../../../domain/user/entities/user-settings.entity");
const user_entity_1 = require("../../../domain/user/entities/user.entity");
const user_mapper_1 = require("../../../domain/user/mappers/user.mapper");
const prisma_service_1 = require("../../prisma/prisma.service");
let SupabaseUserRepository = class SupabaseUserRepository {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getMyProfile(id) {
        const prismaUser = await this.prisma.users.findUnique({ where: { id } });
        if (!prismaUser)
            return null;
        const prismaSettings = await this.prisma.user_settings.findUnique({
            where: { user_id: id },
        });
        return user_mapper_1.UserMapper.toDomain(prismaUser, id, prismaSettings);
    }
    async findById(id) {
        const prismaUser = await this.prisma.users.findUnique({ where: { id } });
        if (!prismaUser)
            return null;
        return new user_entity_1.User(prismaUser.id, prismaUser.id, prismaUser.name, prismaUser.email, prismaUser.password, prismaUser.date_of_birth, prismaUser.gender, prismaUser.allergies, prismaUser.conditions, prismaUser.is_admin || false, prismaUser.phone_number, prismaUser.emergency_contact, prismaUser.created_at || undefined, prismaUser.updated_at || undefined, prismaUser.subscription_status, prismaUser.subscription_plan, prismaUser.subscription_expires_at, prismaUser.subscription_features);
    }
    async update(userAggregate) {
        const { user, settings } = userAggregate;
        const updatedUser = await this.prisma.users.update({
            where: { id: user.id },
            data: {
                name: user.name,
                email: user.email,
                date_of_birth: user.date_of_birth,
                gender: user.gender,
                allergies: user.allergies ?? [],
                conditions: user.conditions ?? [],
                is_admin: user.is_admin,
                phone_number: user.phone_number,
                emergency_contact: user.emergency_contact,
                subscription_status: user.subscription_status,
                subscription_plan: user.subscription_plan,
                subscription_expires_at: user.subscription_expires_at,
                subscription_features: user.subscription_features,
            },
        });
        let updatedSettings = null;
        if (settings) {
            updatedSettings = await this.prisma.user_settings.update({
                where: { user_id: user.id },
                data: {
                    email_enabled: settings.email_enabled,
                    preferred_times: settings.preferred_times,
                    timezone: settings.timezone,
                    notification_preferences: settings.notification_preferences,
                },
            });
        }
        return user_mapper_1.UserMapper.toDomain(updatedUser, userAggregate.authUserId, updatedSettings);
    }
    async delete(id) {
        await this.prisma.user_settings.deleteMany({
            where: { user_id: id },
        });
        await this.prisma.users.delete({
            where: { id },
        });
    }
    async updateSettings(userId, partialSettings) {
        const updateData = { ...partialSettings, updated_at: new Date() };
        delete updateData.user_id;
        const updated = await this.prisma.user_settings.update({
            where: { user_id: userId },
            data: updateData,
        });
        return new user_settings_entity_1.UserSettings(updated.id, updated.user_id, updated.email_enabled, updated.preferred_times, updated.timezone, updated.notification_preferences, updated.created_at || undefined, updated.updated_at || undefined);
    }
    async findAll() {
        try {
            const users = await this.prisma.users.findMany({
                orderBy: { created_at: 'desc' },
            });
            return users.map((user) => new user_entity_1.User(user.id, user.id, user.name, user.email, user.password, user.date_of_birth, user.gender, user.allergies, user.conditions, user.is_admin || false, user.phone_number, user.emergency_contact, user.created_at || undefined, user.updated_at || undefined, user.subscription_status, user.subscription_plan, user.subscription_expires_at, user.subscription_features));
        }
        catch (error) {
            console.error('Error finding all users:', error);
            throw error;
        }
    }
    async findUsersWithEmailNotifications() {
        try {
            const usersWithSettings = await this.prisma.users.findMany({
                include: {
                    settings: true,
                },
                where: {
                    settings: {
                        email_enabled: true,
                    },
                },
                orderBy: { created_at: 'desc' },
            });
            return usersWithSettings.map((user) => new user_entity_1.User(user.id, user.id, user.name, user.email, user.password, user.date_of_birth, user.gender, user.allergies, user.conditions, user.is_admin || false, user.phone_number, user.emergency_contact, user.created_at || undefined, user.updated_at || undefined, user.subscription_status, user.subscription_plan, user.subscription_expires_at, user.subscription_features));
        }
        catch (error) {
            console.error('Error finding users with email notifications:', error);
            throw error;
        }
    }
};
exports.SupabaseUserRepository = SupabaseUserRepository;
exports.SupabaseUserRepository = SupabaseUserRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SupabaseUserRepository);
//# sourceMappingURL=supabase-user.repository.js.map