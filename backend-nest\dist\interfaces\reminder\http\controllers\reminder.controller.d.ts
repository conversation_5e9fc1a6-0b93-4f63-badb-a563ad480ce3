import { CreateReminderUseCase } from 'src/application/reminder/use-cases/create-reminder.usecase';
import { GetUpcomingRemindersUseCase } from 'src/application/reminder/use-cases/get-upcoming-reminders.usecase';
import { GetAllRemindersUseCase } from 'src/application/reminder/use-cases/get-all-reminders.usecase';
import { SendReminderManuallyUseCase } from 'src/application/reminder/use-cases/send-reminder-manually.usecase';
import { DeleteReminderUseCase } from 'src/application/reminder/use-cases/delete-reminder.usecase';
import { UpdateReminderSettingsUseCase } from 'src/application/reminder/use-cases/update-reminder-settings.usecase';
import { GetUserSettingsUseCase } from 'src/application/reminder/use-cases/get-user-settings.usecase';
import { CreateReminderDto } from 'src/interfaces/reminder/dtos/create-reminder.dto';
import { UpdateReminderSettingsDto } from 'src/interfaces/reminder/dtos/update-reminder-settings.dto';
export declare class ReminderController {
    private readonly createReminderUseCase;
    private readonly getUpcomingRemindersUseCase;
    private readonly getAllRemindersUseCase;
    private readonly sendReminderManuallyUseCase;
    private readonly deleteReminderUseCase;
    private readonly updateReminderSettingsUseCase;
    private readonly getUserSettingsUseCase;
    constructor(createReminderUseCase: CreateReminderUseCase, getUpcomingRemindersUseCase: GetUpcomingRemindersUseCase, getAllRemindersUseCase: GetAllRemindersUseCase, sendReminderManuallyUseCase: SendReminderManuallyUseCase, deleteReminderUseCase: DeleteReminderUseCase, updateReminderSettingsUseCase: UpdateReminderSettingsUseCase, getUserSettingsUseCase: GetUserSettingsUseCase);
    getAllReminders(userId: string, startDate?: string, endDate?: string): Promise<{
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: string;
        status: "pending" | "sent" | "failed";
        channels: import("../../../../domain/reminder/entities/reminder.entity").ReminderChannels;
        message: string | null | undefined;
        retry_count: number;
        last_retry: string | null;
        adherence_id: string | null | undefined;
        created_at: string | null;
        updated_at: string | null;
        medication: {
            id: string;
            user_id: string;
            name: string;
            dosage: {
                amount: number;
                unit: string;
            };
            frequency: {
                times_per_day: number;
                specific_days: string[];
            };
            scheduled_times: string[];
            instructions: string | null | undefined;
            start_date: Date | null | undefined;
            end_date: Date | null | undefined;
            refill_reminder: {
                enabled: boolean;
                threshold: number;
                last_refill?: Date | null;
                next_refill?: Date | null;
                supply_amount: number;
                supply_unit: string;
            } | null | undefined;
            side_effects_to_watch: string[];
            active: boolean;
            medication_type: string | null | undefined;
            image_url: string | null | undefined;
            created_at: Date | null | undefined;
            updated_at: Date | null | undefined;
            adherence: any[] | undefined;
            reminders: any[] | undefined;
            user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
        } | null;
        is_overdue: boolean;
        scheduled_datetime: string;
    }[]>;
    getUpcomingReminders(userId: string, limit?: string): Promise<{
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: string;
        status: "pending" | "sent" | "failed";
        channels: import("../../../../domain/reminder/entities/reminder.entity").ReminderChannels;
        message: string | null | undefined;
        retry_count: number;
        last_retry: string | null;
        adherence_id: string | null | undefined;
        created_at: string | null;
        updated_at: string | null;
        medication: {
            id: string;
            user_id: string;
            name: string;
            dosage: {
                amount: number;
                unit: string;
            };
            frequency: {
                times_per_day: number;
                specific_days: string[];
            };
            scheduled_times: string[];
            instructions: string | null | undefined;
            start_date: Date | null | undefined;
            end_date: Date | null | undefined;
            refill_reminder: {
                enabled: boolean;
                threshold: number;
                last_refill?: Date | null;
                next_refill?: Date | null;
                supply_amount: number;
                supply_unit: string;
            } | null | undefined;
            side_effects_to_watch: string[];
            active: boolean;
            medication_type: string | null | undefined;
            image_url: string | null | undefined;
            created_at: Date | null | undefined;
            updated_at: Date | null | undefined;
            adherence: any[] | undefined;
            reminders: any[] | undefined;
            user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
        } | null;
        is_overdue: boolean;
        scheduled_datetime: string;
    }[]>;
    createReminder(reminder: CreateReminderDto, userId: string): Promise<{
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: string;
        status: "pending" | "sent" | "failed";
        channels: import("../../../../domain/reminder/entities/reminder.entity").ReminderChannels;
        message: string | null | undefined;
        retry_count: number;
        last_retry: string | null;
        adherence_id: string | null | undefined;
        created_at: string | null;
        updated_at: string | null;
        medication: {
            id: string;
            user_id: string;
            name: string;
            dosage: {
                amount: number;
                unit: string;
            };
            frequency: {
                times_per_day: number;
                specific_days: string[];
            };
            scheduled_times: string[];
            instructions: string | null | undefined;
            start_date: Date | null | undefined;
            end_date: Date | null | undefined;
            refill_reminder: {
                enabled: boolean;
                threshold: number;
                last_refill?: Date | null;
                next_refill?: Date | null;
                supply_amount: number;
                supply_unit: string;
            } | null | undefined;
            side_effects_to_watch: string[];
            active: boolean;
            medication_type: string | null | undefined;
            image_url: string | null | undefined;
            created_at: Date | null | undefined;
            updated_at: Date | null | undefined;
            adherence: any[] | undefined;
            reminders: any[] | undefined;
            user: import("../../../../domain/user/entities/user-aggregate.entity").UserAggregate | undefined;
        } | null;
        is_overdue: boolean;
        scheduled_datetime: string;
    }>;
    sendReminderManually(id: string, userId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    deleteReminder(id: string, userId: string): Promise<{
        message: string;
    }>;
    getUserSettings(userId: string): Promise<{
        emailEnabled: boolean;
        preferredTimes: string[];
        timezone: string;
        notificationPreferences: any;
    }>;
    updateReminderSettings(settings: UpdateReminderSettingsDto, userId: string): Promise<{
        success: boolean;
        message: string;
        data: {
            emailEnabled: boolean;
            preferredTimes: string[];
            timezone: string;
            notificationPreferences: any;
        };
    }>;
}
