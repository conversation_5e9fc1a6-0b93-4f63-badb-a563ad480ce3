{"version": 3, "file": "sendgrid-notification.service.js", "sourceRoot": "", "sources": ["../../../../src/infrastructure/reminder/services/sendgrid-notification.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,yCAAyC;AACzC,iGAAwF;AAExF,gEAAyE;AAGlE,IAAM,2BAA2B,mCAAjC,MAAM,2BAA4B,SAAQ,0CAAmB;IAI/C;IACA;IAJF,MAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IAEvE,YACmB,aAA4B,EAC5B,MAAqB;QAEtC,KAAK,EAAE,CAAC;QAHS,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;QAGtC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC,CAAC;QAClE,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,QAAkB;QACxC,IAAI,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC1C,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAExD,MAAM,GAAG,GAAG;gBACV,EAAE,EAAE,SAAS,CAAC,EAAE;gBAChB,IAAI,EACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,qBAAqB;gBACvE,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;aACrB,CAAC;YAEF,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAkB;QAEtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,IAAY;QAChD,IAAI,CAAC;YACH,MAAM,GAAG,GAAG;gBACV,EAAE,EAAE,KAAK;gBACT,IAAI,EACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,qBAAqB;gBACvE,OAAO,EAAE,sBAAsB;gBAC/B,IAAI,EAAE,QAAQ,IAAI,iVAAiV;gBACnW,IAAI,EAAE;;oBAEM,IAAI;;;;;;;;;;SAUf;aACF,CAAC;YAEF,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,4BAA4B,CAChC,KAAa,EACb,IAAY,EACZ,IAAY;QAEZ,IAAI,CAAC;YACH,MAAM,GAAG,GAAG;gBACV,EAAE,EAAE,KAAK;gBACT,IAAI,EACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,qBAAqB;gBACvE,OAAO,EAAE,qCAAqC;gBAC9C,IAAI,EAAE,QAAQ,IAAI,4RAA4R;gBAC9S,IAAI,EAAE;;oBAEM,IAAI;;;;;;;;;;SAUf;aACF,CAAC;YAEF,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACnC,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;aACpC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;YAGD,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAE7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACrD,KAAK,EAAE;oBACL,OAAO,EAAE,MAAM;oBACf,cAAc,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE;iBACpC;gBACD,OAAO,EAAE;oBACP,UAAU,EAAE;wBACV,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;qBACrC;iBACF;gBACD,OAAO,EAAE,CAAC,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;aAChE,CAAC,CAAC;YAGH,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC;YACxC,MAAM,kBAAkB,GAAG,SAAS,CAAC,MAAM,CACzC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAC3B,CAAC,MAAM,CAAC;YACT,MAAM,aAAa,GACjB,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAEvE,MAAM,GAAG,GAAG;gBACV,EAAE,EAAE,IAAI,CAAC,KAAM;gBACf,IAAI,EACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,qBAAqB;gBACvE,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE,QAAQ,IAAI,CAAC,IAAI,oFAAoF,cAAc,gCAAgC,kBAAkB,yBAAyB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,qCAAqC;gBACjQ,IAAI,EAAE;;oBAEM,IAAI,CAAC,IAAI;;;2DAG8B,cAAc;8DACX,kBAAkB;uDACzB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;;;SAGtE;aACF,CAAC;YAEF,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AApLY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAKuB,sBAAa;QACpB,8BAAa;GAL7B,2BAA2B,CAoLvC"}