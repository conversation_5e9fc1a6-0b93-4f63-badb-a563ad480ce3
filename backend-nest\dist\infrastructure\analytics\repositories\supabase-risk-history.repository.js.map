{"version": 3, "file": "supabase-risk-history.repository.js", "sourceRoot": "", "sources": ["../../../../src/infrastructure/analytics/repositories/supabase-risk-history.repository.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,gEAA4D;AAIrD,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IACX;IAA7B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,KAAK,CAAC,MAAM,CAAC,MAAyB;QACpC,IAAI,CAAC;YAKH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAA;;0BAExB,MAAM,CAAC,OAAO;OACjC,CAAC;YAEF,IAAI,QAAQ,IAAK,QAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAE/C,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAA;;;;;;;qCAOA,MAAM,CAAC,aAAa;4BAC7B,MAAM,CAAC,IAAI;kCACL,MAAM,CAAC,UAAU;;;4BAGvB,MAAM,CAAC,OAAO;SACjC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBAEN,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;oBACrC,IAAI,EAAE;wBACJ,OAAO,EAAE,MAAM,CAAC,OAAO;wBACvB,aAAa,EAAE,IAAI;wBACnB,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;wBAC5C,QAAQ,EAAE,KAAK;wBACf,wBAAwB,EAAE;4BACxB,YAAY,EAAE,CAAC;oCACb,aAAa,EAAE,MAAM,CAAC,aAAa;oCACnC,IAAI,EAAE,MAAM,CAAC,IAAI;oCACjB,UAAU,EAAE,MAAM,CAAC,UAAU;iCAC9B,CAAC;yBACH;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,MAAc,EACd,YAAoB,EACpB,SAAkB,EAClB,OAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;gBAC1D,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;gBAC1B,MAAM,EAAE,EAAE,wBAAwB,EAAE,IAAI,EAAE;aAC3C,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,wBAAwB,EAAE,CAAC;gBACxC,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,WAAW,GAAI,QAAQ,CAAC,wBAAgC,EAAE,YAAY,IAAI,EAAE,CAAC;YAEnF,OAAO,WAAW;iBACf,MAAM,CAAC,CAAC,MAAW,EAAE,EAAE;gBACtB,IAAI,MAAM,CAAC,aAAa,KAAK,YAAY;oBAAE,OAAO,KAAK,CAAC;gBACxD,IAAI,SAAS,IAAI,MAAM,CAAC,IAAI,GAAG,SAAS;oBAAE,OAAO,KAAK,CAAC;gBACvD,IAAI,OAAO,IAAI,MAAM,CAAC,IAAI,GAAG,OAAO;oBAAE,OAAO,KAAK,CAAC;gBACnD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;gBACrB,OAAO,EAAE,MAAM;gBACf,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC,CAAC,CAAC;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,SAAkB,EAAE,OAAgB;QACnE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;gBAC1D,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;gBAC1B,MAAM,EAAE,EAAE,wBAAwB,EAAE,IAAI,EAAE;aAC3C,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,wBAAwB,EAAE,CAAC;gBACxC,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,WAAW,GAAI,QAAQ,CAAC,wBAAgC,EAAE,YAAY,IAAI,EAAE,CAAC;YAEnF,OAAO,WAAW;iBACf,MAAM,CAAC,CAAC,MAAW,EAAE,EAAE;gBACtB,IAAI,SAAS,IAAI,MAAM,CAAC,IAAI,GAAG,SAAS;oBAAE,OAAO,KAAK,CAAC;gBACvD,IAAI,OAAO,IAAI,MAAM,CAAC,IAAI,GAAG,OAAO;oBAAE,OAAO,KAAK,CAAC;gBACnD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;gBACrB,OAAO,EAAE,MAAM;gBACf,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC,CAAC,CAAC;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,YAAoB;QAC3D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAEtE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,OAAO,MAAM,CAAC,UAAU,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA1IY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;qCAE0B,8BAAa;GADvC,6BAA6B,CA0IzC"}