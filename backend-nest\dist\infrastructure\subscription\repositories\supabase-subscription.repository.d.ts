import { PrismaService } from '../../prisma/prisma.service';
import { SubscriptionRepository } from '../../../domain/subscription/repositories/subscription.repository';
import { Subscription } from '../../../domain/subscription/entities/subscription.entity';
export declare class SupabaseSubscriptionRepository implements SubscriptionRepository {
    private readonly prisma;
    constructor(prisma: PrismaService);
    findByUserId(userId: string): Promise<Subscription | null>;
    updateSubscriptionStatus(userId: string, status: string, plan: string, expiresAt: Date | null, features: Record<string, boolean>, paymentProviderId?: string): Promise<void>;
    create(subscription: Subscription): Promise<Subscription>;
    update(subscription: Subscription): Promise<Subscription>;
}
