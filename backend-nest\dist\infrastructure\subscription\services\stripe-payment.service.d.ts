import { ConfigService } from '@nestjs/config';
import { PaymentProvider, CreateCheckoutSessionRequest, CheckoutSessionResponse, WebhookResult, PaymentDetails } from '../../../domain/subscription/services/payment-provider.interface';
export declare class StripePaymentService implements PaymentProvider {
    private readonly configService;
    private stripe;
    constructor(configService: ConfigService);
    createCheckoutSession(request: CreateCheckoutSessionRequest): Promise<CheckoutSessionResponse>;
    handleWebhook(payload: any, signature?: string): Promise<WebhookResult>;
    private handleCheckoutSessionCompleted;
    private handleSubscriptionUpdated;
    private handleSubscriptionDeleted;
    getPaymentDetails(paymentId: string): Promise<PaymentDetails>;
}
