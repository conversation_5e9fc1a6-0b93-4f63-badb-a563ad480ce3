"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdherenceController = void 0;
const common_1 = require("@nestjs/common");
const confirm_dose_usecase_1 = require("../../../../application/adherence/use-cases/confirm-dose.usecase");
const get_adherence_history_usecase_1 = require("../../../../application/adherence/use-cases/get-adherence-history.usecase");
const skip_dose_usecase_1 = require("../../../../application/adherence/use-cases/skip-dose.usecase");
const get_adherence_stats_usecase_1 = require("../../../../application/adherence/use-cases/get-adherence-stats.usecase");
const get_user_id_decorator_1 = require("../../../common/decorators/get-user-id.decorator");
const jwt_auth_guard_1 = require("../../../common/guards/jwt-auth.guard");
const adherence_presenter_1 = require("../../../../domain/adherence/presenters/adherence.presenter");
const confirm_dose_dto_1 = require("../../dtos/confirm-dose.dto");
const get_adherence_history_dto_1 = require("../../dtos/get-adherence-history.dto");
const skip_dose_dto_1 = require("../../dtos/skip-dose.dto");
const get_adherence_stats_dto_1 = require("../../dtos/get-adherence-stats.dto");
const subscription_guard_1 = require("../../../common/guards/subscription.guard");
let AdherenceController = class AdherenceController {
    getAdherenceHistoryUseCase;
    confirmDoseUseCase;
    skipDoseUseCase;
    getAdherenceStatsUseCase;
    constructor(getAdherenceHistoryUseCase, confirmDoseUseCase, skipDoseUseCase, getAdherenceStatsUseCase) {
        this.getAdherenceHistoryUseCase = getAdherenceHistoryUseCase;
        this.confirmDoseUseCase = confirmDoseUseCase;
        this.skipDoseUseCase = skipDoseUseCase;
        this.getAdherenceStatsUseCase = getAdherenceStatsUseCase;
    }
    async getHistory(query, userId) {
        const adherences = await this.getAdherenceHistoryUseCase.execute(userId, query.date);
        return adherence_presenter_1.AdherencePresenter.toHttpList(adherences);
    }
    async confirmDose(body, userId) {
        const adherence = await this.confirmDoseUseCase.execute(body.adherenceId, userId);
        return adherence_presenter_1.AdherencePresenter.toHttp(adherence);
    }
    async skipDose(body, userId) {
        const adherence = await this.skipDoseUseCase.execute(body.adherenceId, userId);
        return adherence_presenter_1.AdherencePresenter.toHttp(adherence);
    }
    async getStats(query, userId) {
        return await this.getAdherenceStatsUseCase.execute(userId, query.startDate, query.endDate);
    }
};
exports.AdherenceController = AdherenceController;
__decorate([
    (0, common_1.Get)('history'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_adherence_history_dto_1.GetAdherenceHistoryDto, String]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "getHistory", null);
__decorate([
    (0, common_1.Post)('confirm'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [confirm_dose_dto_1.ConfirmDoseDto, String]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "confirmDose", null);
__decorate([
    (0, common_1.Post)('skip'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [skip_dose_dto_1.SkipDoseDto, String]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "skipDose", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, subscription_guard_1.SubscriptionGuard),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_adherence_stats_dto_1.GetAdherenceStatsDto, String]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "getStats", null);
exports.AdherenceController = AdherenceController = __decorate([
    (0, common_1.Controller)('adherence'),
    __metadata("design:paramtypes", [get_adherence_history_usecase_1.GetAdherenceHistoryUseCase,
        confirm_dose_usecase_1.ConfirmDoseUseCase,
        skip_dose_usecase_1.SkipDoseUseCase,
        get_adherence_stats_usecase_1.GetAdherenceStatsUseCase])
], AdherenceController);
//# sourceMappingURL=adherence.controller.js.map