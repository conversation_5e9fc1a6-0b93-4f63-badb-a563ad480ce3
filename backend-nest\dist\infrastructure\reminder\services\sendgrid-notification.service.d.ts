import { ConfigService } from '@nestjs/config';
import { NotificationService } from 'src/domain/reminder/services/notification.service';
import { Reminder } from 'src/domain/reminder/entities/reminder.entity';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
export declare class SendGridNotificationService extends NotificationService {
    private readonly configService;
    private readonly prisma;
    private readonly logger;
    constructor(configService: ConfigService, prisma: PrismaService);
    sendEmailReminder(reminder: Reminder): Promise<boolean>;
    sendSMSReminder(reminder: Reminder): Promise<boolean>;
    sendWelcomeEmail(email: string, name: string): Promise<boolean>;
    sendSubscriptionConfirmation(email: string, name: string, plan: string): Promise<boolean>;
    sendWeeklyReport(userId: string): Promise<boolean>;
}
