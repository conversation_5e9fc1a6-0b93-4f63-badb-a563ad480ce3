"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModule = void 0;
const common_1 = require("@nestjs/common");
const user_controller_1 = require("./http/controllers/user.controller");
const supabase_user_repository_1 = require("../../infrastructure/user/repositories/supabase-user.repository");
const get_me_usecase_1 = require("../../application/user/use-cases/get-me.usecase");
const update_user_usecase_1 = require("../../application/user/use-cases/update-user.usecase");
const delete_user_usecase_1 = require("../../application/user/use-cases/delete-user.usecase");
const update_user_settings_usecase_1 = require("../../application/user/use-cases/update-user-settings.usecase");
const prisma_service_1 = require("../../infrastructure/prisma/prisma.service");
let UserModule = class UserModule {
};
exports.UserModule = UserModule;
exports.UserModule = UserModule = __decorate([
    (0, common_1.Module)({
        controllers: [user_controller_1.UserController],
        providers: [
            prisma_service_1.PrismaService,
            get_me_usecase_1.GetMeUseCase,
            update_user_usecase_1.UpdateUserUseCase,
            delete_user_usecase_1.DeleteUserUseCase,
            update_user_settings_usecase_1.UpdateUserSettingsUseCase,
            {
                provide: 'UserRepository',
                useClass: supabase_user_repository_1.SupabaseUserRepository,
            },
        ],
    })
], UserModule);
//# sourceMappingURL=user.module.js.map