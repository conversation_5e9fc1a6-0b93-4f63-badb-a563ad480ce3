import React, { createContext, useContext, useEffect } from 'react';
import { User, AuthContextType } from '@/types';
import { useAuth as useSupabaseAuth, useSignIn, useSignUp, useSignOut } from '@/hooks/useAuth';
import { supabase } from '@/config/supabase';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user, session, isAuthenticated, isLoading } = useSupabaseAuth();
  const signInMutation = useSignIn();
  const signUpMutation = useSignUp();
  const signOutMutation = useSignOut();

  // Listen for auth state changes
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session);
    });

    return () => subscription.unsubscribe();
  }, []);

  const login = async (email: string, password: string) => {
    return signInMutation.mutateAsync({ email, password });
  };

  const register = async (name: string, email: string, password: string) => {
    return signUpMutation.mutateAsync({ name, email, password });
  };

  const logout = async () => {
    return signOutMutation.mutateAsync();
  };

  return (
    <AuthContext.Provider value={{
      user,
      isLoading: isLoading || signInMutation.isPending || signUpMutation.isPending || signOutMutation.isPending,
      login,
      register,
      logout
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};