"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdherenceModule = void 0;
const common_1 = require("@nestjs/common");
const get_adherence_history_usecase_1 = require("../../application/adherence/use-cases/get-adherence-history.usecase");
const confirm_dose_usecase_1 = require("../../application/adherence/use-cases/confirm-dose.usecase");
const skip_dose_usecase_1 = require("../../application/adherence/use-cases/skip-dose.usecase");
const get_adherence_stats_usecase_1 = require("../../application/adherence/use-cases/get-adherence-stats.usecase");
const adherence_stats_service_1 = require("../../domain/adherence/services/adherence-stats.service");
const prisma_service_1 = require("../../infrastructure/prisma/prisma.service");
const supabase_adherence_repository_1 = require("../../infrastructure/adherence/repositories/supabase-adherence.repository");
const supabase_subscription_repository_1 = require("../../infrastructure/subscription/repositories/supabase-subscription.repository");
const adherence_controller_1 = require("./http/controllers/adherence.controller");
let AdherenceModule = class AdherenceModule {
};
exports.AdherenceModule = AdherenceModule;
exports.AdherenceModule = AdherenceModule = __decorate([
    (0, common_1.Module)({
        controllers: [adherence_controller_1.AdherenceController],
        providers: [
            prisma_service_1.PrismaService,
            get_adherence_history_usecase_1.GetAdherenceHistoryUseCase,
            confirm_dose_usecase_1.ConfirmDoseUseCase,
            skip_dose_usecase_1.SkipDoseUseCase,
            get_adherence_stats_usecase_1.GetAdherenceStatsUseCase,
            adherence_stats_service_1.AdherenceStatsService,
            {
                provide: 'AdherenceRepository',
                useClass: supabase_adherence_repository_1.SupabaseAdherenceRepository,
            },
            {
                provide: 'SubscriptionRepository',
                useClass: supabase_subscription_repository_1.SupabaseSubscriptionRepository,
            },
        ],
    })
], AdherenceModule);
//# sourceMappingURL=adherence.module.js.map