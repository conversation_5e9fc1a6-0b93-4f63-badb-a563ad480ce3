"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfirmDoseUseCase = void 0;
const common_1 = require("@nestjs/common");
let ConfirmDoseUseCase = class ConfirmDoseUseCase {
    adherenceRepository;
    constructor(adherenceRepository) {
        this.adherenceRepository = adherenceRepository;
    }
    async execute(adherenceId, userId) {
        const existingAdherence = await this.adherenceRepository.findById(adherenceId);
        if (!existingAdherence) {
            throw new Error('Adherence record not found');
        }
        if (existingAdherence.user_id !== userId) {
            throw new Error('Unauthorized access to adherence record');
        }
        if (existingAdherence.status !== 'pending') {
            throw new Error('Adherence record is not in pending status');
        }
        return await this.adherenceRepository.confirmDose(adherenceId, userId);
    }
};
exports.ConfirmDoseUseCase = ConfirmDoseUseCase;
exports.ConfirmDoseUseCase = ConfirmDoseUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('AdherenceRepository')),
    __metadata("design:paramtypes", [Object])
], ConfirmDoseUseCase);
//# sourceMappingURL=confirm-dose.usecase.js.map