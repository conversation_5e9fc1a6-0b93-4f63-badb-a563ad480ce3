"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseReminderRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const reminder_mapper_1 = require("../../../domain/reminder/mappers/reminder.mapper");
let SupabaseReminderRepository = class SupabaseReminderRepository {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(reminder) {
        const created = await this.prisma.reminders.create({
            data: {
                user_id: reminder.user_id,
                medication_id: reminder.medication_id,
                scheduled_time: reminder.scheduled_time,
                scheduled_date: new Date(reminder.scheduled_date),
                status: reminder.status || 'pending',
                channels: (reminder.channels || {
                    email: { enabled: true, sent: false },
                    sms: { enabled: false, sent: false },
                }),
                message: reminder.message,
                retry_count: reminder.retry_count || 0,
                last_retry: reminder.last_retry ? new Date(reminder.last_retry) : null,
                adherence_id: reminder.adherence_id,
            },
            include: {
                medication: true,
                user: true,
            },
        });
        return reminder_mapper_1.ReminderMapper.toDomain(created);
    }
    async update(reminder) {
        const updateData = {};
        if (reminder.scheduled_time !== undefined)
            updateData.scheduled_time = reminder.scheduled_time;
        if (reminder.scheduled_date !== undefined)
            updateData.scheduled_date = new Date(reminder.scheduled_date);
        if (reminder.status !== undefined)
            updateData.status = reminder.status;
        if (reminder.channels !== undefined)
            updateData.channels = reminder.channels;
        if (reminder.message !== undefined)
            updateData.message = reminder.message;
        if (reminder.retry_count !== undefined)
            updateData.retry_count = reminder.retry_count;
        if (reminder.last_retry !== undefined)
            updateData.last_retry = reminder.last_retry
                ? new Date(reminder.last_retry)
                : null;
        const updated = await this.prisma.reminders.update({
            where: { id: reminder.id },
            data: updateData,
            include: {
                medication: true,
                user: true,
            },
        });
        return reminder_mapper_1.ReminderMapper.toDomain(updated);
    }
    async delete(id) {
        await this.prisma.reminders.delete({
            where: { id },
        });
        return { message: 'Reminder deleted successfully' };
    }
    async findById(id) {
        const found = await this.prisma.reminders.findUnique({
            where: { id },
            include: {
                medication: true,
                user: true,
            },
        });
        if (!found)
            return null;
        return reminder_mapper_1.ReminderMapper.toDomain(found);
    }
    async findByUser(userId, startDate, endDate) {
        const whereClause = { user_id: userId };
        if (startDate) {
            whereClause.scheduled_date = {
                ...whereClause.scheduled_date,
                gte: new Date(startDate),
            };
        }
        if (endDate) {
            whereClause.scheduled_date = {
                ...whereClause.scheduled_date,
                lte: new Date(endDate),
            };
        }
        const found = await this.prisma.reminders.findMany({
            where: whereClause,
            include: {
                medication: true,
                user: true,
            },
            orderBy: [{ scheduled_date: 'asc' }, { scheduled_time: 'asc' }],
        });
        return reminder_mapper_1.ReminderMapper.toDomainList(found);
    }
    async findUpcomingByUser(userId, limit = 10) {
        const now = new Date();
        const today = now.toISOString().split('T')[0];
        const currentTime = now.toTimeString().slice(0, 5);
        const found = await this.prisma.reminders.findMany({
            where: {
                user_id: userId,
                status: 'pending',
                OR: [
                    { scheduled_date: { gt: new Date(today) } },
                    {
                        scheduled_date: new Date(today),
                        scheduled_time: { gte: currentTime },
                    },
                ],
            },
            include: {
                medication: true,
                user: true,
            },
            orderBy: [{ scheduled_date: 'asc' }, { scheduled_time: 'asc' }],
            take: limit,
        });
        return reminder_mapper_1.ReminderMapper.toDomainList(found);
    }
    async findPendingReminders(userId, date, startTime, endTime) {
        const whereClause = { status: 'pending' };
        if (userId) {
            whereClause.user_id = userId;
        }
        if (date) {
            whereClause.scheduled_date = new Date(date);
        }
        if (startTime && endTime) {
            whereClause.scheduled_time = {
                gte: startTime,
                lte: endTime,
            };
        }
        const found = await this.prisma.reminders.findMany({
            where: whereClause,
            include: {
                medication: true,
                user: true,
            },
            orderBy: [{ scheduled_date: 'asc' }, { scheduled_time: 'asc' }],
        });
        return reminder_mapper_1.ReminderMapper.toDomainList(found);
    }
    async findOverdueReminders(userId) {
        const now = new Date();
        const today = now.toISOString().split('T')[0];
        const currentTime = now.toTimeString().slice(0, 5);
        const whereClause = {
            status: 'pending',
            OR: [
                { scheduled_date: { lt: new Date(today) } },
                {
                    scheduled_date: new Date(today),
                    scheduled_time: { lt: currentTime },
                },
            ],
        };
        if (userId) {
            whereClause.user_id = userId;
        }
        const found = await this.prisma.reminders.findMany({
            where: whereClause,
            include: {
                medication: true,
                user: true,
            },
            orderBy: [{ scheduled_date: 'desc' }, { scheduled_time: 'desc' }],
        });
        return reminder_mapper_1.ReminderMapper.toDomainList(found);
    }
    async findByMedication(medicationId) {
        const found = await this.prisma.reminders.findMany({
            where: { medication_id: medicationId },
            include: {
                medication: true,
                user: true,
            },
            orderBy: [{ scheduled_date: 'asc' }, { scheduled_time: 'asc' }],
        });
        return reminder_mapper_1.ReminderMapper.toDomainList(found);
    }
    async findByAdherence(adherenceId) {
        const found = await this.prisma.reminders.findFirst({
            where: { adherence_id: adherenceId },
            include: {
                medication: true,
                user: true,
            },
        });
        if (!found)
            return null;
        return reminder_mapper_1.ReminderMapper.toDomain(found);
    }
    async markAsSent(id, channel) {
        const reminder = await this.findById(id);
        if (!reminder)
            throw new Error('Reminder not found');
        const updatedChannels = { ...reminder.channels };
        updatedChannels[channel].sent = true;
        updatedChannels[channel].sentAt = new Date().toISOString();
        return this.update({
            id,
            status: 'sent',
            channels: updatedChannels,
            last_retry: new Date().toISOString(),
        });
    }
    async markAsFailed(id) {
        const reminder = await this.findById(id);
        if (!reminder)
            throw new Error('Reminder not found');
        return this.update({
            id,
            status: 'failed',
            retry_count: reminder.retry_count + 1,
            last_retry: new Date().toISOString(),
        });
    }
    async bulkCreate(reminders) {
        await this.prisma.reminders.createMany({
            data: reminders.map((reminder) => ({
                user_id: reminder.user_id,
                medication_id: reminder.medication_id,
                scheduled_time: reminder.scheduled_time,
                scheduled_date: new Date(reminder.scheduled_date),
                status: reminder.status || 'pending',
                channels: (reminder.channels || {
                    email: { enabled: true, sent: false },
                    sms: { enabled: false, sent: false },
                }),
                message: reminder.message,
                retry_count: reminder.retry_count || 0,
                last_retry: reminder.last_retry ? new Date(reminder.last_retry) : null,
                adherence_id: reminder.adherence_id,
            })),
        });
        const createdReminders = await this.prisma.reminders.findMany({
            where: {
                user_id: { in: reminders.map((r) => r.user_id) },
                medication_id: { in: reminders.map((r) => r.medication_id) },
                created_at: { gte: new Date(Date.now() - 5000) },
            },
            include: {
                medication: true,
                user: true,
            },
        });
        return reminder_mapper_1.ReminderMapper.toDomainList(createdReminders);
    }
    async deleteByMedication(medicationId, fromDate) {
        const whereClause = {
            medication_id: medicationId,
            status: 'pending',
        };
        if (fromDate) {
            whereClause.scheduled_date = { gte: fromDate };
        }
        const result = await this.prisma.reminders.deleteMany({
            where: whereClause,
        });
        return { count: result.count };
    }
};
exports.SupabaseReminderRepository = SupabaseReminderRepository;
exports.SupabaseReminderRepository = SupabaseReminderRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SupabaseReminderRepository);
//# sourceMappingURL=supabase-reminder.repository.js.map