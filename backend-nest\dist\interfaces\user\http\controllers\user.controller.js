"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const delete_user_usecase_1 = require("../../../../application/user/use-cases/delete-user.usecase");
const get_me_usecase_1 = require("../../../../application/user/use-cases/get-me.usecase");
const update_user_settings_usecase_1 = require("../../../../application/user/use-cases/update-user-settings.usecase");
const update_user_usecase_1 = require("../../../../application/user/use-cases/update-user.usecase");
const get_user_id_decorator_1 = require("../../../common/decorators/get-user-id.decorator");
const jwt_auth_guard_1 = require("../../../common/guards/jwt-auth.guard");
const user_mapper_1 = require("../../../../domain/user/mappers/user.mapper");
const user_presenter_1 = require("../../../../domain/user/presenters/user.presenter");
const update_user_settings_dto_1 = require("../../dtos/update-user-settings.dto");
const update_user_dto_1 = require("../../dtos/update-user.dto");
let UserController = class UserController {
    updateUserUseCase;
    updateUserSettingsUseCase;
    getMeUseCase;
    deleteUserUseCase;
    constructor(updateUserUseCase, updateUserSettingsUseCase, getMeUseCase, deleteUserUseCase) {
        this.updateUserUseCase = updateUserUseCase;
        this.updateUserSettingsUseCase = updateUserSettingsUseCase;
        this.getMeUseCase = getMeUseCase;
        this.deleteUserUseCase = deleteUserUseCase;
    }
    async getMyProfile(userId) {
        const userAggregate = await this.getMeUseCase.execute(userId);
        return user_presenter_1.UserPresenter.toHttp(userAggregate);
    }
    async delete(id) {
        await this.deleteUserUseCase.execute(id);
        return { message: 'User deleted successfully' };
    }
    async updateUser(id, updateUserDto) {
        const userAggregate = user_mapper_1.UserMapper.toDomain({ ...updateUserDto, id }, '');
        const updatedUser = await this.updateUserUseCase.execute(userAggregate);
        return user_presenter_1.UserPresenter.toHttp(updatedUser);
    }
    async updateSettings(userId, updateSettingsDto) {
        const updatedSettings = await this.updateUserSettingsUseCase.execute(userId, updateSettingsDto);
        return user_presenter_1.UserPresenter.toSettingsJson(updatedSettings);
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Get)('me'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getMyProfile", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "delete", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateUser", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Patch)('me/settings'),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_settings_dto_1.UpdateUserSettingsDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "updateSettings", null);
exports.UserController = UserController = __decorate([
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [update_user_usecase_1.UpdateUserUseCase,
        update_user_settings_usecase_1.UpdateUserSettingsUseCase,
        get_me_usecase_1.GetMeUseCase,
        delete_user_usecase_1.DeleteUserUseCase])
], UserController);
//# sourceMappingURL=user.controller.js.map