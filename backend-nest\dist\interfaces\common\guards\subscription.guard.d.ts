import { CanActivate, ExecutionContext } from '@nestjs/common';
import { SubscriptionRepository } from 'src/domain/subscription/repositories/subscription.repository';
export declare class SubscriptionGuard implements CanActivate {
    private readonly subscriptionRepository;
    constructor(subscriptionRepository: SubscriptionRepository);
    canActivate(context: ExecutionContext): Promise<boolean>;
}
