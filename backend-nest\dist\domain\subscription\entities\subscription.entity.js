"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionFeatures = exports.SubscriptionPlan = exports.SubscriptionStatus = exports.Subscription = void 0;
class Subscription {
    id;
    userId;
    status;
    plan;
    expiresAt;
    features;
    paymentProviderId;
    createdAt;
    updatedAt;
    constructor(id, userId, status, plan, expiresAt, features, paymentProviderId, createdAt, updatedAt) {
        this.id = id;
        this.userId = userId;
        this.status = status;
        this.plan = plan;
        this.expiresAt = expiresAt;
        this.features = features;
        this.paymentProviderId = paymentProviderId;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    isActive() {
        return (this.status === SubscriptionStatus.PREMIUM &&
            this.expiresAt !== null &&
            this.expiresAt > new Date());
    }
    isPremium() {
        return this.plan === SubscriptionPlan.PREMIUM && this.isActive();
    }
    hasFeature(feature) {
        return this.isPremium() && this.features[feature] === true;
    }
    updateToPremium(expirationDate, paymentProviderId) {
        this.status = SubscriptionStatus.PREMIUM;
        this.plan = SubscriptionPlan.PREMIUM;
        this.expiresAt = expirationDate;
        this.paymentProviderId = paymentProviderId;
        this.features = SubscriptionFeatures.createPremiumFeatures();
        this.updatedAt = new Date();
    }
    downgradeToFree() {
        this.status = SubscriptionStatus.FREE;
        this.plan = SubscriptionPlan.FREE;
        this.expiresAt = null;
        this.paymentProviderId = null;
        this.features = SubscriptionFeatures.createFreeFeatures();
        this.updatedAt = new Date();
    }
    isExpired() {
        return this.expiresAt !== null && this.expiresAt <= new Date();
    }
}
exports.Subscription = Subscription;
var SubscriptionStatus;
(function (SubscriptionStatus) {
    SubscriptionStatus["FREE"] = "free";
    SubscriptionStatus["PREMIUM"] = "premium";
})(SubscriptionStatus || (exports.SubscriptionStatus = SubscriptionStatus = {}));
var SubscriptionPlan;
(function (SubscriptionPlan) {
    SubscriptionPlan["FREE"] = "free";
    SubscriptionPlan["PREMIUM"] = "premium";
})(SubscriptionPlan || (exports.SubscriptionPlan = SubscriptionPlan = {}));
class SubscriptionFeatures {
    smsReminders;
    customSounds;
    priorityNotifications;
    familyNotifications;
    weeklyReports;
    advancedAnalytics;
    riskScoring;
    constructor(smsReminders = false, customSounds = false, priorityNotifications = false, familyNotifications = false, weeklyReports = false, advancedAnalytics = false, riskScoring = false) {
        this.smsReminders = smsReminders;
        this.customSounds = customSounds;
        this.priorityNotifications = priorityNotifications;
        this.familyNotifications = familyNotifications;
        this.weeklyReports = weeklyReports;
        this.advancedAnalytics = advancedAnalytics;
        this.riskScoring = riskScoring;
    }
    static createPremiumFeatures() {
        return new SubscriptionFeatures(true, true, true, true, true, true, true);
    }
    static createFreeFeatures() {
        return new SubscriptionFeatures(false, false, false, false, false, false, false);
    }
    static fromJson(json) {
        return new SubscriptionFeatures(json.smsReminders || false, json.customSounds || false, json.priorityNotifications || false, json.familyNotifications || false, json.weeklyReports || false, json.advancedAnalytics || false, json.riskScoring || false);
    }
    toJson() {
        return {
            smsReminders: this.smsReminders,
            customSounds: this.customSounds,
            priorityNotifications: this.priorityNotifications,
            familyNotifications: this.familyNotifications,
        };
    }
}
exports.SubscriptionFeatures = SubscriptionFeatures;
//# sourceMappingURL=subscription.entity.js.map