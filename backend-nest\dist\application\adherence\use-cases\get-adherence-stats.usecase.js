"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetAdherenceStatsUseCase = void 0;
const common_1 = require("@nestjs/common");
const adherence_stats_service_1 = require("../../../domain/adherence/services/adherence-stats.service");
let GetAdherenceStatsUseCase = class GetAdherenceStatsUseCase {
    adherenceRepository;
    adherenceStatsService;
    constructor(adherenceRepository, adherenceStatsService) {
        this.adherenceRepository = adherenceRepository;
        this.adherenceStatsService = adherenceStatsService;
    }
    async execute(userId, startDate, endDate) {
        let finalStartDate = startDate;
        let finalEndDate = endDate;
        if (!startDate || !endDate) {
            const defaultRange = this.adherenceStatsService.getDefaultDateRange();
            finalStartDate = startDate || defaultRange.startDate;
            finalEndDate = endDate || defaultRange.endDate;
        }
        const rawData = await this.adherenceRepository.getStats(userId, finalStartDate, finalEndDate);
        return this.adherenceStatsService.processStats(rawData);
    }
};
exports.GetAdherenceStatsUseCase = GetAdherenceStatsUseCase;
exports.GetAdherenceStatsUseCase = GetAdherenceStatsUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('AdherenceRepository')),
    __metadata("design:paramtypes", [Object, adherence_stats_service_1.AdherenceStatsService])
], GetAdherenceStatsUseCase);
//# sourceMappingURL=get-adherence-stats.usecase.js.map