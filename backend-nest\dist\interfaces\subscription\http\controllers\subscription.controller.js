"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../../../common/guards/jwt-auth.guard");
const get_user_id_decorator_1 = require("../../../common/decorators/get-user-id.decorator");
const create_checkout_session_dto_1 = require("../dtos/create-checkout-session.dto");
const create_checkout_session_usecase_1 = require("../../../../application/subscription/use-cases/create-checkout-session.usecase");
const get_subscription_status_usecase_1 = require("../../../../application/subscription/use-cases/get-subscription-status.usecase");
const handle_webhook_usecase_1 = require("../../../../application/subscription/use-cases/handle-webhook.usecase");
const handle_success_redirect_usecase_1 = require("../../../../application/subscription/use-cases/handle-success-redirect.usecase");
const payment_provider_interface_1 = require("../../../../domain/subscription/services/payment-provider.interface");
let SubscriptionController = class SubscriptionController {
    createCheckoutSessionUseCase;
    getSubscriptionStatusUseCase;
    handleWebhookUseCase;
    handleSuccessRedirectUseCase;
    constructor(createCheckoutSessionUseCase, getSubscriptionStatusUseCase, handleWebhookUseCase, handleSuccessRedirectUseCase) {
        this.createCheckoutSessionUseCase = createCheckoutSessionUseCase;
        this.getSubscriptionStatusUseCase = getSubscriptionStatusUseCase;
        this.handleWebhookUseCase = handleWebhookUseCase;
        this.handleSuccessRedirectUseCase = handleSuccessRedirectUseCase;
    }
    async createCheckoutSession(userId, createCheckoutSessionDto) {
        return await this.createCheckoutSessionUseCase.execute({
            userId,
            ...createCheckoutSessionDto,
        });
    }
    async getSubscriptionStatus(userId) {
        return await this.getSubscriptionStatusUseCase.execute({ userId });
    }
    async handleStripeWebhook(payload, signature) {
        return await this.handleWebhookUseCase.execute({
            paymentProvider: payment_provider_interface_1.PaymentProviderType.STRIPE,
            payload,
            signature,
        });
    }
    async handleMercadoPagoWebhook(body, query) {
        const payload = { ...query, ...body };
        return await this.handleWebhookUseCase.execute({
            paymentProvider: payment_provider_interface_1.PaymentProviderType.MERCADOPAGO,
            payload,
        });
    }
    async handleSuccess(query, res) {
        const result = await this.handleSuccessRedirectUseCase.execute({
            collectionStatus: query.collection_status,
            status: query.status,
            externalReference: query.external_reference,
            paymentId: query.payment_id,
            collectionId: query.collection_id,
        });
        res.redirect(result.redirectUrl);
    }
    async handleFailure(externalReference, res) {
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
        res.redirect(`${frontendUrl}/subscription/failure?userId=${externalReference}`);
    }
    async handlePending(externalReference, res) {
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
        res.redirect(`${frontendUrl}/subscription/pending?userId=${externalReference}`);
    }
};
exports.SubscriptionController = SubscriptionController;
__decorate([
    (0, common_1.Post)('create-checkout-session'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_checkout_session_dto_1.CreateCheckoutSessionDto]),
    __metadata("design:returntype", Promise)
], SubscriptionController.prototype, "createCheckoutSession", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SubscriptionController.prototype, "getSubscriptionStatus", null);
__decorate([
    (0, common_1.Post)('webhooks/stripe'),
    __param(0, (0, common_1.RawBody)()),
    __param(1, (0, common_1.Headers)('stripe-signature')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Buffer, String]),
    __metadata("design:returntype", Promise)
], SubscriptionController.prototype, "handleStripeWebhook", null);
__decorate([
    (0, common_1.Post)('mercadopago/webhook'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SubscriptionController.prototype, "handleMercadoPagoWebhook", null);
__decorate([
    (0, common_1.Get)('success'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SubscriptionController.prototype, "handleSuccess", null);
__decorate([
    (0, common_1.Get)('failure'),
    __param(0, (0, common_1.Query)('external_reference')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SubscriptionController.prototype, "handleFailure", null);
__decorate([
    (0, common_1.Get)('pending'),
    __param(0, (0, common_1.Query)('external_reference')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SubscriptionController.prototype, "handlePending", null);
exports.SubscriptionController = SubscriptionController = __decorate([
    (0, common_1.Controller)('subscriptions'),
    __metadata("design:paramtypes", [create_checkout_session_usecase_1.CreateCheckoutSessionUseCase,
        get_subscription_status_usecase_1.GetSubscriptionStatusUseCase,
        handle_webhook_usecase_1.HandleWebhookUseCase,
        handle_success_redirect_usecase_1.HandleSuccessRedirectUseCase])
], SubscriptionController);
//# sourceMappingURL=subscription.controller.js.map