"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdherenceStatsService = void 0;
const common_1 = require("@nestjs/common");
let AdherenceStatsService = class AdherenceStatsService {
    processStats(rawData) {
        const stats = {
            total: rawData.length,
            taken: 0,
            missed: 0,
            skipped: 0,
            pending: 0,
            adherenceRate: 0,
            byMedication: {},
        };
        rawData.forEach((record) => {
            switch (record.status) {
                case 'taken':
                    stats.taken++;
                    break;
                case 'missed':
                    stats.missed++;
                    break;
                case 'skipped':
                    stats.skipped++;
                    break;
                case 'pending':
                default:
                    stats.pending++;
                    break;
            }
        });
        const completedDoses = stats.taken + stats.missed + stats.skipped;
        stats.adherenceRate =
            completedDoses > 0 ? (stats.taken / completedDoses) * 100 : 0;
        rawData.forEach((record) => {
            const medId = record.medication.id;
            if (!stats.byMedication[medId]) {
                stats.byMedication[medId] = {
                    id: medId,
                    name: record.medication.name,
                    total: 0,
                    taken: 0,
                    missed: 0,
                    skipped: 0,
                    pending: 0,
                };
            }
            const medStats = stats.byMedication[medId];
            medStats.total++;
            switch (record.status) {
                case 'taken':
                    medStats.taken++;
                    break;
                case 'missed':
                    medStats.missed++;
                    break;
                case 'skipped':
                    medStats.skipped++;
                    break;
                case 'pending':
                default:
                    medStats.pending++;
                    break;
            }
        });
        return stats;
    }
    getDefaultDateRange() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);
        return {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0],
        };
    }
};
exports.AdherenceStatsService = AdherenceStatsService;
exports.AdherenceStatsService = AdherenceStatsService = __decorate([
    (0, common_1.Injectable)()
], AdherenceStatsService);
//# sourceMappingURL=adherence-stats.service.js.map