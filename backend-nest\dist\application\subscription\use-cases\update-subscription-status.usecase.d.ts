import { SubscriptionRepository } from '../../../domain/subscription/repositories/subscription.repository';
import { SubscriptionDomainService } from '../../../domain/subscription/services/subscription-domain.service';
import { SubscriptionStatus } from '../../../domain/subscription/entities/subscription.entity';
export interface UpdateSubscriptionStatusCommand {
    userId: string;
    subscriptionId?: string;
    status: SubscriptionStatus;
    months?: number;
}
export declare class UpdateSubscriptionStatusUseCase {
    private readonly subscriptionRepository;
    private readonly subscriptionDomainService;
    constructor(subscriptionRepository: SubscriptionRepository, subscriptionDomainService: SubscriptionDomainService);
    execute(command: UpdateSubscriptionStatusCommand): Promise<void>;
}
