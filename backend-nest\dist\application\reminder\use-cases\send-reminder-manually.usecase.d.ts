import { ReminderRepository } from 'src/domain/reminder/repositories/reminder.repository';
import { NotificationService } from 'src/domain/reminder/services/notification.service';
import { UserRepository } from 'src/domain/user/repositories/user.repository';
export declare class SendReminderManuallyUseCase {
    private readonly reminderRepository;
    private readonly notificationService;
    private readonly userRepository;
    constructor(reminderRepository: ReminderRepository, notificationService: NotificationService, userRepository: UserRepository);
    execute(reminderId: string, userId: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
