import { AdherenceRepository } from '../../../domain/adherence/repositories/adherence.repository';
import { AdherenceStats } from '../../../domain/adherence/entities/adherence-stats.entity';
import { AdherenceStatsService } from '../../../domain/adherence/services/adherence-stats.service';
export declare class GetAdherenceStatsUseCase {
    private readonly adherenceRepository;
    private readonly adherenceStatsService;
    constructor(adherenceRepository: AdherenceRepository, adherenceStatsService: AdherenceStatsService);
    execute(userId: string, startDate?: string, endDate?: string): Promise<AdherenceStats>;
}
