import { UserAggregate } from '../entities/user-aggregate.entity';
export declare class UserPresenter {
    static toHttp(aggregate: UserAggregate): {
        settings: {
            id: any;
            user_id: any;
            email_enabled: any;
            preferred_times: any;
            timezone: any;
            notification_preferences: any;
            created_at: any;
            updated_at: any;
        } | null;
        name: any;
        email: any;
        date_of_birth: any;
        gender: any;
        allergies: any;
        conditions: any;
        is_admin: any;
        phone_number: any;
        emergency_contact: any;
        created_at: any;
        updated_at: any;
        subscription_status: any;
        subscription_plan: any;
        subscription_expires_at: any;
        subscription_features: any;
        id: string;
        authUserId: any;
    };
    static toUserJson(user: any): {
        id: any;
        auth_user_id: any;
        name: any;
        email: any;
        date_of_birth: any;
        gender: any;
        allergies: any;
        conditions: any;
        is_admin: any;
        phone_number: any;
        emergency_contact: any;
        created_at: any;
        updated_at: any;
        subscription_status: any;
        subscription_plan: any;
        subscription_expires_at: any;
        subscription_features: any;
    };
    static toSettingsJson(settings: any): {
        id: any;
        user_id: any;
        email_enabled: any;
        preferred_times: any;
        timezone: any;
        notification_preferences: any;
        created_at: any;
        updated_at: any;
    };
}
