import { UserAggregate } from 'src/domain/user/entities/user-aggregate.entity';
import { UserSettings } from 'src/domain/user/entities/user-settings.entity';
import { User } from 'src/domain/user/entities/user.entity';
import { UserRepository } from 'src/domain/user/repositories/user.repository';
import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
export declare class SupabaseUserRepository implements UserRepository {
    private readonly prisma;
    constructor(prisma: PrismaService);
    getMyProfile(id: string): Promise<UserAggregate | null>;
    findById(id: string): Promise<User | null>;
    update(userAggregate: UserAggregate): Promise<UserAggregate>;
    delete(id: string): Promise<void>;
    updateSettings(userId: string, partialSettings: Partial<UserSettings>): Promise<UserSettings>;
    findAll(): Promise<User[]>;
    findUsersWithEmailNotifications(): Promise<User[]>;
}
