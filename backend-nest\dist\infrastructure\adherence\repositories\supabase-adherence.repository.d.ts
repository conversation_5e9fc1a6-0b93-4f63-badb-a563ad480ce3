import { AdherenceRepository } from '../../../domain/adherence/repositories/adherence.repository';
import { Adherence } from '../../../domain/adherence/entities/adherence.entity';
import { AdherenceStatsRaw } from '../../../domain/adherence/entities/adherence-stats.entity';
import { PrismaService } from '../../prisma/prisma.service';
export declare class SupabaseAdherenceRepository implements AdherenceRepository {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(adherence: Adherence): Promise<Adherence>;
    update(adherence: Adherence): Promise<Adherence>;
    delete(id: string): Promise<void>;
    findById(id: string): Promise<Adherence | null>;
    findByUser(userId: string): Promise<Adherence[]>;
    findActiveByUser(userId: string): Promise<Adherence[]>;
    getHistory(userId: string, date?: string): Promise<Adherence[]>;
    confirmDose(adherenceId: string, userId: string): Promise<Adherence>;
    skipDose(adherenceId: string, userId: string): Promise<Adherence>;
    getStats(userId: string, startDate?: string, endDate?: string): Promise<AdherenceStatsRaw[]>;
    findPendingForMissedProcessing(todayStr: string, cutoffTime: Date): Promise<Adherence[]>;
    findByUserMedicationDateRange(userId: string, medicationId: string, startDate: string, endDate: string): Promise<Adherence[]>;
    updateStatus(adherenceId: string, status: string): Promise<void>;
}
