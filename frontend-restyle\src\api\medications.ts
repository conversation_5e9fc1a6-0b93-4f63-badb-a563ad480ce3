import apiClient from "../config/api";
import { Medication } from "../types";

export interface CreateMedicationDto {
  name: string;
  dosage: {
    amount: number;
    unit: string;
    form: string;
  };
  frequency: {
    type: 'daily' | 'weekly' | 'as_needed';
    interval: number;
    times_per_day?: number;
  };
  scheduled_times: string[];
  instructions?: string;
  start_date: string;
  end_date?: string;
  refill_reminder?: {
    enabled: boolean;
    days_before: number;
  };
  side_effects_to_watch: string[];
  medication_type?: 'prescription' | 'otc' | 'supplement';
}

// Get all medications for the current user
export const getMedications = async (): Promise<Medication[]> => {
  const response = await apiClient.get("/medications");
  return response.data;
};

// Get active medications for the current user
export const getActiveMedications = async (): Promise<Medication[]> => {
  const response = await apiClient.get("/medications/active");
  return response.data;
};

// Get medication by ID
export const getMedicationById = async (id: string): Promise<Medication> => {
  const response = await apiClient.get(`/medications/${id}`);
  return response.data;
};

// Create new medication (with adherence records)
export const createMedication = async (medication: CreateMedicationDto): Promise<Medication> => {
  const response = await apiClient.post("/medications", medication);
  return response.data;
};

// Create simple medication (without adherence records)
export const createSimpleMedication = async (medication: CreateMedicationDto): Promise<Medication> => {
  const response = await apiClient.post("/medications/simple", medication);
  return response.data;
};

// Update medication
export const updateMedication = async (id: string, medication: Partial<CreateMedicationDto>): Promise<Medication> => {
  const response = await apiClient.put(`/medications/${id}`, medication);
  return response.data;
};

// Delete medication
export const deleteMedication = async (id: string): Promise<void> => {
  await apiClient.delete(`/medications/${id}`);
};
