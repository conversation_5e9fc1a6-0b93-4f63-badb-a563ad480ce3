"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const common_1 = require("@nestjs/common");
let NotificationService = class NotificationService {
    formatDosage(dosage) {
        if (typeof dosage === 'object' && dosage.amount && dosage.unit) {
            return `${dosage.amount} ${dosage.unit}`;
        }
        return dosage?.toString() || 'N/A';
    }
    buildEmailReminderData(reminder) {
        const dosage = this.formatDosage(reminder.medication?.dosage);
        const userName = reminder.user?.user?.name || 'Usuario';
        const medicationName = reminder.medication?.name || 'Medicamento';
        const instructions = reminder.medication?.instructions || 'Sin instrucciones específicas';
        return {
            to: reminder.user?.user?.email || '',
            subject: 'Recordatorio de Medicamento',
            text: `Hola ${userName},\n\nEs hora de tomar tu medicamento:\n\nMedicamento: ${medicationName}\nDosis: ${dosage}\nInstrucciones: ${instructions}\n\nSaludos,\nTu equipo de MedCare`,
            html: `
        <h2>Recordatorio de Medicamento</h2>
        <p>Hola ${userName},</p>
        <p>Es hora de tomar tu medicamento:</p>
        <ul>
          <li><strong>Medicamento:</strong> ${medicationName}</li>
          <li><strong>Dosis:</strong> ${dosage}</li>
          <li><strong>Instrucciones:</strong> ${instructions}</li>
        </ul>
        <p>Saludos,<br>Tu equipo de MedCare</p>
      `,
        };
    }
    buildSMSReminderData(reminder) {
        const dosage = this.formatDosage(reminder.medication?.dosage);
        const medicationName = reminder.medication?.name || 'Medicamento';
        return {
            to: reminder.user?.user?.phone_number || '',
            message: `MedCare: Es hora de tomar ${medicationName} - ${dosage}. ¡No olvides tu medicamento!`,
        };
    }
};
exports.NotificationService = NotificationService;
exports.NotificationService = NotificationService = __decorate([
    (0, common_1.Injectable)()
], NotificationService);
//# sourceMappingURL=notification.service.js.map