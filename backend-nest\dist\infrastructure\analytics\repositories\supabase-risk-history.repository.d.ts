import { PrismaService } from '../../prisma/prisma.service';
import { RiskHistoryRepository, RiskHistoryRecord } from '../../../domain/analytics/repositories/risk-history.repository';
export declare class SupabaseRiskHistoryRepository implements RiskHistoryRepository {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(record: RiskHistoryRecord): Promise<void>;
    findByUserMedication(userId: string, medicationId: string, startDate?: string, endDate?: string): Promise<RiskHistoryRecord[]>;
    findByUser(userId: string, startDate?: string, endDate?: string): Promise<RiskHistoryRecord[]>;
    getLatestRiskScore(userId: string, medicationId: string): Promise<number | null>;
}
