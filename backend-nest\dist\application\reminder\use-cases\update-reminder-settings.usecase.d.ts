import { PrismaService } from 'src/infrastructure/prisma/prisma.service';
import { UpdateReminderSettingsDto } from 'src/interfaces/reminder/dtos/update-reminder-settings.dto';
export declare class UpdateReminderSettingsUseCase {
    private readonly prisma;
    constructor(prisma: PrismaService);
    execute(userId: string, settings: UpdateReminderSettingsDto): Promise<{
        success: boolean;
        message: string;
        data: {
            emailEnabled: boolean;
            preferredTimes: string[];
            timezone: string;
            notificationPreferences: any;
        };
    }>;
}
