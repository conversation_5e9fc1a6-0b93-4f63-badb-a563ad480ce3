"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AppSchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppSchedulerService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const process_missed_adherence_usecase_1 = require("../../../application/adherence/use-cases/process-missed-adherence.usecase");
const calculate_daily_risk_scores_usecase_1 = require("../../../application/analytics/use-cases/calculate-daily-risk-scores.usecase");
const generate_weekly_reports_usecase_1 = require("../../../application/reports/use-cases/generate-weekly-reports.usecase");
let AppSchedulerService = AppSchedulerService_1 = class AppSchedulerService {
    processMissedAdherenceUseCase;
    calculateDailyRiskScoresUseCase;
    generateWeeklyReportsUseCase;
    logger = new common_1.Logger(AppSchedulerService_1.name);
    constructor(processMissedAdherenceUseCase, calculateDailyRiskScoresUseCase, generateWeeklyReportsUseCase) {
        this.processMissedAdherenceUseCase = processMissedAdherenceUseCase;
        this.calculateDailyRiskScoresUseCase = calculateDailyRiskScoresUseCase;
        this.generateWeeklyReportsUseCase = generateWeeklyReportsUseCase;
    }
    onModuleInit() {
        this.logger.log('App Scheduler Service initialized - All cron jobs are set up');
    }
    async handleMissedAdherenceProcessing() {
        try {
            this.logger.log('Running missed adherence processing job');
            const result = await this.processMissedAdherenceUseCase.execute();
            this.logger.log(`Missed adherence job completed: ${JSON.stringify(result)}`);
        }
        catch (error) {
            this.logger.error(`Error in missed adherence job: ${error.message}`, error.stack);
        }
    }
    async handleDailyRiskScoreCalculation() {
        try {
            this.logger.log('Running daily risk score calculation job');
            const result = await this.calculateDailyRiskScoresUseCase.execute();
            this.logger.log(`Daily risk score job completed: ${JSON.stringify(result)}`);
        }
        catch (error) {
            this.logger.error(`Error in daily risk score job: ${error.message}`, error.stack);
        }
    }
    async handleWeeklyReportGeneration() {
        try {
            this.logger.log('Running weekly report generation job');
            const result = await this.generateWeeklyReportsUseCase.execute();
            this.logger.log(`Weekly report job completed: ${JSON.stringify(result)}`);
        }
        catch (error) {
            this.logger.error(`Error in weekly report job: ${error.message}`, error.stack);
        }
    }
    async triggerMissedAdherenceProcessing() {
        this.logger.log('Manually triggering missed adherence processing');
        return this.processMissedAdherenceUseCase.execute();
    }
    async triggerDailyRiskScoreCalculation() {
        this.logger.log('Manually triggering daily risk score calculation');
        return this.calculateDailyRiskScoresUseCase.execute();
    }
    async triggerWeeklyReportGeneration() {
        this.logger.log('Manually triggering weekly report generation');
        return this.generateWeeklyReportsUseCase.execute();
    }
};
exports.AppSchedulerService = AppSchedulerService;
__decorate([
    (0, schedule_1.Cron)('0 * * * *', {
        name: 'process-missed-adherence',
        timeZone: 'UTC',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppSchedulerService.prototype, "handleMissedAdherenceProcessing", null);
__decorate([
    (0, schedule_1.Cron)('0 3 * * *', {
        name: 'calculate-daily-risk-scores',
        timeZone: 'UTC',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppSchedulerService.prototype, "handleDailyRiskScoreCalculation", null);
__decorate([
    (0, schedule_1.Cron)('0 9 * * 0', {
        name: 'generate-weekly-reports',
        timeZone: 'UTC',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppSchedulerService.prototype, "handleWeeklyReportGeneration", null);
exports.AppSchedulerService = AppSchedulerService = AppSchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [process_missed_adherence_usecase_1.ProcessMissedAdherenceUseCase,
        calculate_daily_risk_scores_usecase_1.CalculateDailyRiskScoresUseCase,
        generate_weekly_reports_usecase_1.GenerateWeeklyReportsUseCase])
], AppSchedulerService);
//# sourceMappingURL=app-scheduler.service.js.map