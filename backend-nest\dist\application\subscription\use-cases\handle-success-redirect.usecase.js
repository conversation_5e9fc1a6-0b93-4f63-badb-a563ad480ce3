"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HandleSuccessRedirectUseCase = void 0;
const common_1 = require("@nestjs/common");
const update_subscription_status_usecase_1 = require("./update-subscription-status.usecase");
const subscription_entity_1 = require("../../../domain/subscription/entities/subscription.entity");
let HandleSuccessRedirectUseCase = class HandleSuccessRedirectUseCase {
    updateSubscriptionStatusUseCase;
    constructor(updateSubscriptionStatusUseCase) {
        this.updateSubscriptionStatusUseCase = updateSubscriptionStatusUseCase;
    }
    async execute(command) {
        try {
            const { collectionStatus, status, externalReference, paymentId, collectionId } = command;
            console.log('Success route called:', {
                collectionStatus,
                status,
                externalReference,
                paymentId,
                collectionId
            });
            if ((collectionStatus === 'approved' || status === 'approved') && externalReference) {
                console.log('Payment approved in success route, updating subscription for user:', externalReference);
                try {
                    await this.updateSubscriptionStatusUseCase.execute({
                        userId: externalReference,
                        subscriptionId: paymentId || collectionId,
                        status: subscription_entity_1.SubscriptionStatus.PREMIUM,
                    });
                    console.log('Subscription updated successfully from success route');
                }
                catch (updateError) {
                    console.error('Error updating subscription from success route:', updateError);
                }
            }
            const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
            const redirectUrl = `${frontendUrl}/subscription/success?userId=${externalReference}&paymentId=${paymentId || collectionId}`;
            return { redirectUrl };
        }
        catch (error) {
            console.error('Error in success route:', error);
            const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
            return { redirectUrl: `${frontendUrl}/subscription/failure` };
        }
    }
};
exports.HandleSuccessRedirectUseCase = HandleSuccessRedirectUseCase;
exports.HandleSuccessRedirectUseCase = HandleSuccessRedirectUseCase = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [update_subscription_status_usecase_1.UpdateSubscriptionStatusUseCase])
], HandleSuccessRedirectUseCase);
//# sourceMappingURL=handle-success-redirect.usecase.js.map