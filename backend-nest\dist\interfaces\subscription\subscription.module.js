"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionModule = void 0;
const common_1 = require("@nestjs/common");
const subscription_controller_1 = require("./http/controllers/subscription.controller");
const supabase_subscription_repository_1 = require("../../infrastructure/subscription/repositories/supabase-subscription.repository");
const stripe_payment_service_1 = require("../../infrastructure/subscription/services/stripe-payment.service");
const mercadopago_payment_service_1 = require("../../infrastructure/subscription/services/mercadopago-payment.service");
const subscription_domain_service_1 = require("../../domain/subscription/services/subscription-domain.service");
const create_checkout_session_usecase_1 = require("../../application/subscription/use-cases/create-checkout-session.usecase");
const get_subscription_status_usecase_1 = require("../../application/subscription/use-cases/get-subscription-status.usecase");
const handle_webhook_usecase_1 = require("../../application/subscription/use-cases/handle-webhook.usecase");
const handle_success_redirect_usecase_1 = require("../../application/subscription/use-cases/handle-success-redirect.usecase");
const update_subscription_status_usecase_1 = require("../../application/subscription/use-cases/update-subscription-status.usecase");
const prisma_service_1 = require("../../infrastructure/prisma/prisma.service");
let SubscriptionModule = class SubscriptionModule {
};
exports.SubscriptionModule = SubscriptionModule;
exports.SubscriptionModule = SubscriptionModule = __decorate([
    (0, common_1.Module)({
        controllers: [subscription_controller_1.SubscriptionController],
        providers: [
            prisma_service_1.PrismaService,
            subscription_domain_service_1.SubscriptionDomainService,
            create_checkout_session_usecase_1.CreateCheckoutSessionUseCase,
            get_subscription_status_usecase_1.GetSubscriptionStatusUseCase,
            handle_webhook_usecase_1.HandleWebhookUseCase,
            handle_success_redirect_usecase_1.HandleSuccessRedirectUseCase,
            update_subscription_status_usecase_1.UpdateSubscriptionStatusUseCase,
            {
                provide: 'SubscriptionRepository',
                useClass: supabase_subscription_repository_1.SupabaseSubscriptionRepository,
            },
            {
                provide: 'StripePaymentProvider',
                useClass: stripe_payment_service_1.StripePaymentService,
            },
            {
                provide: 'MercadoPagoPaymentProvider',
                useClass: mercadopago_payment_service_1.MercadoPagoPaymentService,
            },
        ],
        exports: ['SubscriptionRepository', subscription_domain_service_1.SubscriptionDomainService],
    })
], SubscriptionModule);
//# sourceMappingURL=subscription.module.js.map