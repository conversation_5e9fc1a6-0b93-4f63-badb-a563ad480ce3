import { Reminder } from '../entities/reminder.entity';
export declare class ReminderPresenter {
    static toHttp(reminder: <PERSON>minder): {
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: string;
        status: "pending" | "sent" | "failed";
        channels: import("../entities/reminder.entity").ReminderChannels;
        message: string | null | undefined;
        retry_count: number;
        last_retry: string | null;
        adherence_id: string | null | undefined;
        created_at: string | null;
        updated_at: string | null;
        medication: {
            id: string;
            user_id: string;
            name: string;
            dosage: {
                amount: number;
                unit: string;
            };
            frequency: {
                times_per_day: number;
                specific_days: string[];
            };
            scheduled_times: string[];
            instructions: string | null | undefined;
            start_date: Date | null | undefined;
            end_date: Date | null | undefined;
            refill_reminder: {
                enabled: boolean;
                threshold: number;
                last_refill?: Date | null;
                next_refill?: Date | null;
                supply_amount: number;
                supply_unit: string;
            } | null | undefined;
            side_effects_to_watch: string[];
            active: boolean;
            medication_type: string | null | undefined;
            image_url: string | null | undefined;
            created_at: Date | null | undefined;
            updated_at: Date | null | undefined;
            adherence: any[] | undefined;
            reminders: any[] | undefined;
            user: import("../../user/entities/user-aggregate.entity").UserAggregate | undefined;
        } | null;
        is_overdue: boolean;
        scheduled_datetime: string;
    };
    static toHttpList(reminders: Reminder[]): {
        id: string;
        user_id: string;
        medication_id: string;
        scheduled_time: string;
        scheduled_date: string;
        status: "pending" | "sent" | "failed";
        channels: import("../entities/reminder.entity").ReminderChannels;
        message: string | null | undefined;
        retry_count: number;
        last_retry: string | null;
        adherence_id: string | null | undefined;
        created_at: string | null;
        updated_at: string | null;
        medication: {
            id: string;
            user_id: string;
            name: string;
            dosage: {
                amount: number;
                unit: string;
            };
            frequency: {
                times_per_day: number;
                specific_days: string[];
            };
            scheduled_times: string[];
            instructions: string | null | undefined;
            start_date: Date | null | undefined;
            end_date: Date | null | undefined;
            refill_reminder: {
                enabled: boolean;
                threshold: number;
                last_refill?: Date | null;
                next_refill?: Date | null;
                supply_amount: number;
                supply_unit: string;
            } | null | undefined;
            side_effects_to_watch: string[];
            active: boolean;
            medication_type: string | null | undefined;
            image_url: string | null | undefined;
            created_at: Date | null | undefined;
            updated_at: Date | null | undefined;
            adherence: any[] | undefined;
            reminders: any[] | undefined;
            user: import("../../user/entities/user-aggregate.entity").UserAggregate | undefined;
        } | null;
        is_overdue: boolean;
        scheduled_datetime: string;
    }[];
    static toHttpSummary(reminder: Reminder): {
        id: string;
        medication_name: string;
        scheduled_time: string;
        scheduled_date: string;
        status: "pending" | "sent" | "failed";
        is_overdue: boolean;
    };
    static toHttpSummaryList(reminders: Reminder[]): {
        id: string;
        medication_name: string;
        scheduled_time: string;
        scheduled_date: string;
        status: "pending" | "sent" | "failed";
        is_overdue: boolean;
    }[];
}
