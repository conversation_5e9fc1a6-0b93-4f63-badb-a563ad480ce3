"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateReminderSettingsUseCase = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../../infrastructure/prisma/prisma.service");
let UpdateReminderSettingsUseCase = class UpdateReminderSettingsUseCase {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async execute(userId, settings) {
        const data = await this.prisma.user_settings.upsert({
            where: { user_id: userId },
            update: {
                email_enabled: settings.emailEnabled,
                preferred_times: settings.preferredTimes,
                timezone: settings.timezone || 'UTC',
                notification_preferences: settings.notificationPreferences,
                updated_at: new Date(),
            },
            create: {
                user_id: userId,
                email_enabled: settings.emailEnabled ?? true,
                preferred_times: settings.preferredTimes || ['08:00', '14:00', '20:00'],
                timezone: settings.timezone || 'UTC',
                notification_preferences: settings.notificationPreferences || {
                    email: true,
                    push: false,
                    sms: false,
                },
            },
            select: {
                email_enabled: true,
                preferred_times: true,
                timezone: true,
                notification_preferences: true,
            },
        });
        return {
            success: true,
            message: 'Settings updated successfully',
            data: {
                emailEnabled: data.email_enabled,
                preferredTimes: data.preferred_times,
                timezone: data.timezone,
                notificationPreferences: data.notification_preferences,
            },
        };
    }
};
exports.UpdateReminderSettingsUseCase = UpdateReminderSettingsUseCase;
exports.UpdateReminderSettingsUseCase = UpdateReminderSettingsUseCase = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UpdateReminderSettingsUseCase);
//# sourceMappingURL=update-reminder-settings.usecase.js.map