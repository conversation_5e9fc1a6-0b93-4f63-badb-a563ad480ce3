import { SubscriptionRepository } from '../../../domain/subscription/repositories/subscription.repository';
import { SubscriptionPresentation } from '../../../domain/subscription/presenters/subscription.presenter';
import { SubscriptionDomainService } from '../../../domain/subscription/services/subscription-domain.service';
export interface GetSubscriptionStatusQuery {
    userId: string;
}
export declare class GetSubscriptionStatusUseCase {
    private readonly subscriptionRepository;
    private readonly subscriptionDomainService;
    constructor(subscriptionRepository: SubscriptionRepository, subscriptionDomainService: SubscriptionDomainService);
    execute(query: GetSubscriptionStatusQuery): Promise<SubscriptionPresentation>;
}
