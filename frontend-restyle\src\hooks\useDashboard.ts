import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { useActiveMedications } from "./useMedications";
import {
  useAdherenceHistory,
  useAdherenceStats,
  useConfirmDose,
  useSkipDose,
} from "./useAdherence";
import { useUpcomingReminders } from "./useReminders";
import { useAuth } from "./useAuth";

// Hook for today's adherence schedule
export const useTodaySchedule = () => {
  const { user } = useAuth();
  const today = format(new Date(), "yyyy-MM-dd");

  const { data: adherenceHistory, isLoading: adherenceLoading } =
    useAdherenceHistory(today);
  const { data: medications, isLoading: medicationsLoading } =
    useActiveMedications();

  return useQuery({
    queryKey: ["dashboard", "todaySchedule", today],
    queryFn: () => {
      if (!medications || !adherenceHistory) return [];

      // Create today's schedule by combining medications with adherence data
      const schedule = medications.flatMap((medication) =>
        medication.scheduled_times.map((time) => {
          // Find corresponding adherence record
          const adherenceRecord = adherenceHistory.find(
            (adh) =>
              adh.medication_id === medication.id && adh.scheduled_time === time
          );

          return {
            id: adherenceRecord?.id || `${medication.id}-${time}`,
            medication,
            scheduled_time: time,
            scheduled_date: today,
            status: adherenceRecord?.status || "pending",
            taken_time: adherenceRecord?.taken_time,
            notes: adherenceRecord?.notes,
            adherence_id: adherenceRecord?.id,
          };
        })
      );

      return schedule.sort((a, b) =>
        a.scheduled_time.localeCompare(b.scheduled_time)
      );
    },
    enabled:
      !!user &&
      !adherenceLoading &&
      !medicationsLoading &&
      !!medications &&
      !!adherenceHistory,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Hook for dashboard statistics
export const useDashboardStats = () => {
  const { user } = useAuth();
  const { data: adherenceStats, isLoading: statsLoading } = useAdherenceStats();
  const { data: medications, isLoading: medicationsLoading } =
    useActiveMedications();
  const { data: todaySchedule, isLoading: scheduleLoading } =
    useTodaySchedule();

  return useQuery({
    queryKey: ["dashboard", "stats"],
    queryFn: () => {
      if (!adherenceStats || !medications || !todaySchedule) return null;

      const completedToday = todaySchedule.filter(
        (item) => item.status === "taken"
      ).length;
      const totalToday = todaySchedule.length;
      const pendingToday = todaySchedule.filter(
        (item) => item.status === "pending"
      );

      return {
        today: {
          completed: completedToday,
          total: totalToday,
          percentage:
            totalToday > 0
              ? Math.round((completedToday / totalToday) * 100)
              : 0,
          pending: pendingToday.length,
        },
        week: adherenceStats.week,
        month: adherenceStats.month,
        activeMedications: medications.length,
        ranking: adherenceStats.ranking,
      };
    },
    enabled:
      !!user &&
      !statsLoading &&
      !medicationsLoading &&
      !scheduleLoading &&
      !!adherenceStats &&
      !!medications &&
      !!todaySchedule,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook for dashboard alerts and notifications
export const useDashboardAlerts = () => {
  const { user } = useAuth();
  const { data: medications } = useActiveMedications();
  const { data: upcomingReminders } = useUpcomingReminders();
  const { data: todaySchedule } = useTodaySchedule();

  return useQuery({
    queryKey: ["dashboard", "alerts"],
    queryFn: () => {
      const alerts = [];

      // Refill reminders
      if (medications) {
        medications.forEach((medication) => {
          if (medication.refill_reminder?.enabled) {
            // Simulate refill check (in real app, this would come from backend)
            const daysUntilRefill = Math.floor(Math.random() * 10) + 1;
            if (daysUntilRefill <= medication.refill_reminder.days_before) {
              alerts.push({
                id: `refill-${medication.id}`,
                type: "refill",
                priority: "warning",
                title: "Refill Reminder",
                message: `${
                  medication.name
                } refill needed in ${daysUntilRefill} day${
                  daysUntilRefill !== 1 ? "s" : ""
                }`,
                medication_id: medication.id,
              });
            }
          }
        });
      }

      // Upcoming doses
      if (todaySchedule) {
        const now = new Date();
        const currentTime = format(now, "HH:mm");

        const nextDose = todaySchedule.find(
          (item) =>
            item.status === "pending" && item.scheduled_time > currentTime
        );

        if (nextDose) {
          alerts.push({
            id: `upcoming-${nextDose.id}`,
            type: "upcoming",
            priority: "info",
            title: "Upcoming Dose",
            message: `${nextDose.medication.name} at ${nextDose.scheduled_time}`,
            medication_id: nextDose.medication.id,
          });
        }
      }

      // Adherence streak (positive alert)
      const streak = Math.floor(Math.random() * 10) + 1; // Simulate streak
      if (streak >= 7) {
        alerts.push({
          id: "streak",
          type: "achievement",
          priority: "success",
          title: "Great Progress!",
          message: `${streak}-day streak of perfect adherence`,
        });
      }

      return alerts;
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for medication actions on dashboard
export const useDashboardActions = () => {
  const confirmDoseMutation = useConfirmDose();
  const skipDoseMutation = useSkipDose();

  const handleMedicationAction = async (
    adherenceId: string,
    action: "take" | "skip",
    notes?: string
  ) => {
    try {
      if (action === "take") {
        await confirmDoseMutation.mutateAsync({
          adherence_id: adherenceId,
          taken_time: new Date().toISOString(),
          notes,
        });
      } else {
        await skipDoseMutation.mutateAsync({
          adherence_id: adherenceId,
          notes: notes || "Skipped via dashboard",
        });
      }
    } catch (error) {
      console.error("Failed to update medication status:", error);
      throw error;
    }
  };

  return {
    handleMedicationAction,
    isLoading: confirmDoseMutation.isPending || skipDoseMutation.isPending,
  };
};
