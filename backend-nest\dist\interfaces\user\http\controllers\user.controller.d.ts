import { DeleteUserUseCase } from 'src/application/user/use-cases/delete-user.usecase';
import { GetMeUseCase } from 'src/application/user/use-cases/get-me.usecase';
import { UpdateUserSettingsUseCase } from 'src/application/user/use-cases/update-user-settings.usecase';
import { UpdateUserUseCase } from 'src/application/user/use-cases/update-user.usecase';
import { UpdateUserSettingsDto } from 'src/interfaces/user/dtos/update-user-settings.dto';
import { UpdateUserDto } from 'src/interfaces/user/dtos/update-user.dto';
export declare class UserController {
    readonly updateUserUseCase: UpdateUserUseCase;
    readonly updateUserSettingsUseCase: UpdateUserSettingsUseCase;
    readonly getMeUseCase: GetMeUseCase;
    readonly deleteUserUseCase: DeleteUserUseCase;
    constructor(updateUserUseCase: UpdateUserUseCase, updateUserSettingsUseCase: UpdateUserSettingsUseCase, getMeUseCase: GetMeUseCase, deleteUserUseCase: DeleteUserUseCase);
    getMyProfile(userId: string): Promise<{
        settings: {
            id: any;
            user_id: any;
            email_enabled: any;
            preferred_times: any;
            timezone: any;
            notification_preferences: any;
            created_at: any;
            updated_at: any;
        } | null;
        name: any;
        email: any;
        date_of_birth: any;
        gender: any;
        allergies: any;
        conditions: any;
        is_admin: any;
        phone_number: any;
        emergency_contact: any;
        created_at: any;
        updated_at: any;
        subscription_status: any;
        subscription_plan: any;
        subscription_expires_at: any;
        subscription_features: any;
        id: string;
        authUserId: any;
    }>;
    delete(id: string): Promise<{
        message: string;
    }>;
    updateUser(id: string, updateUserDto: UpdateUserDto): Promise<{
        settings: {
            id: any;
            user_id: any;
            email_enabled: any;
            preferred_times: any;
            timezone: any;
            notification_preferences: any;
            created_at: any;
            updated_at: any;
        } | null;
        name: any;
        email: any;
        date_of_birth: any;
        gender: any;
        allergies: any;
        conditions: any;
        is_admin: any;
        phone_number: any;
        emergency_contact: any;
        created_at: any;
        updated_at: any;
        subscription_status: any;
        subscription_plan: any;
        subscription_expires_at: any;
        subscription_features: any;
        id: string;
        authUserId: any;
    }>;
    updateSettings(userId: string, updateSettingsDto: UpdateUserSettingsDto): Promise<{
        id: any;
        user_id: any;
        email_enabled: any;
        preferred_times: any;
        timezone: any;
        notification_preferences: any;
        created_at: any;
        updated_at: any;
    }>;
}
