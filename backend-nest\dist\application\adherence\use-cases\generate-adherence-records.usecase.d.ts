import { AdherenceRepository } from '../../../domain/adherence/repositories/adherence.repository';
import { MedicationRepository } from '../../../domain/medication/repositories/medication.repository';
import { UserRepository } from '../../../domain/user/repositories/user.repository';
import { AdherenceGenerationService } from '../../../domain/adherence/services/adherence-generation.service';
export interface GenerateAdherenceRecordsResult {
    usersProcessed: number;
    medicationsProcessed: number;
    recordsGenerated: number;
    errors: number;
}
export declare class GenerateAdherenceRecordsUseCase {
    private readonly adherenceRepository;
    private readonly medicationRepository;
    private readonly userRepository;
    private readonly adherenceGenerationService;
    constructor(adherenceRepository: AdherenceRepository, medicationRepository: MedicationRepository, userRepository: UserRepository, adherenceGenerationService: AdherenceGenerationService);
    execute(): Promise<GenerateAdherenceRecordsResult>;
    private generateAdherenceForMedication;
    private shouldTakeMedicationOnDay;
}
