"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseAdherenceRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const adherence_mapper_1 = require("../../../domain/adherence/mappers/adherence.mapper");
let SupabaseAdherenceRepository = class SupabaseAdherenceRepository {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(adherence) {
        const data = {
            id: adherence.id,
            user_id: adherence.user_id,
            medication_id: adherence.medication_id,
            scheduled_time: adherence.scheduled_time,
            scheduled_date: adherence.scheduled_date,
            taken_time: adherence.taken_time,
            status: adherence.status,
            notes: adherence.notes,
            reminder_sent: adherence.reminder_sent,
            side_effects_reported: adherence.side_effects_reported,
            dosage_taken: adherence.dosage_taken,
            created_at: adherence.created_at,
            updated_at: adherence.updated_at,
        };
        const created = await this.prisma.adherence.create({
            data,
            include: {
                medication: true,
                user: true,
                reminders: true,
            },
        });
        return adherence_mapper_1.AdherenceMapper.toDomain(created);
    }
    async update(adherence) {
        const data = {
            user_id: adherence.user_id,
            medication_id: adherence.medication_id,
            scheduled_time: adherence.scheduled_time,
            scheduled_date: adherence.scheduled_date,
            taken_time: adherence.taken_time,
            status: adherence.status,
            notes: adherence.notes,
            reminder_sent: adherence.reminder_sent,
            side_effects_reported: adherence.side_effects_reported,
            dosage_taken: adherence.dosage_taken,
            updated_at: new Date(),
        };
        const updated = await this.prisma.adherence.update({
            where: { id: adherence.id },
            data,
            include: {
                medication: true,
                user: true,
                reminders: true,
            },
        });
        return adherence_mapper_1.AdherenceMapper.toDomain(updated);
    }
    async delete(id) {
        await this.prisma.adherence.delete({
            where: { id },
        });
    }
    async findById(id) {
        const adherence = await this.prisma.adherence.findUnique({
            where: { id },
            include: {
                medication: true,
                user: true,
                reminders: true,
            },
        });
        return adherence ? adherence_mapper_1.AdherenceMapper.toDomain(adherence) : null;
    }
    async findByUser(userId) {
        const adherences = await this.prisma.adherence.findMany({
            where: { user_id: userId },
            include: {
                medication: true,
                user: true,
                reminders: true,
            },
            orderBy: {
                scheduled_date: 'desc',
            },
        });
        return adherences.map(adherence_mapper_1.AdherenceMapper.toDomain);
    }
    async findActiveByUser(userId) {
        const adherences = await this.prisma.adherence.findMany({
            where: {
                user_id: userId,
                status: 'pending',
            },
            include: {
                medication: true,
                user: true,
                reminders: true,
            },
            orderBy: {
                scheduled_date: 'asc',
            },
        });
        return adherences.map(adherence_mapper_1.AdherenceMapper.toDomain);
    }
    async getHistory(userId, date) {
        const whereClause = {
            user_id: userId,
        };
        if (date) {
            whereClause.scheduled_date = new Date(date);
        }
        const adherences = await this.prisma.adherence.findMany({
            where: whereClause,
            include: {
                medication: {
                    select: {
                        id: true,
                        name: true,
                        dosage: true,
                        instructions: true,
                    },
                },
            },
            orderBy: {
                scheduled_time: 'asc',
            },
        });
        return adherences.map(adherence_mapper_1.AdherenceMapper.toDomain);
    }
    async confirmDose(adherenceId, userId) {
        const updated = await this.prisma.adherence.update({
            where: {
                id: adherenceId,
                user_id: userId,
            },
            data: {
                status: 'taken',
                taken_time: new Date(),
                updated_at: new Date(),
            },
            include: {
                medication: true,
                user: true,
                reminders: true,
            },
        });
        return adherence_mapper_1.AdherenceMapper.toDomain(updated);
    }
    async skipDose(adherenceId, userId) {
        const updated = await this.prisma.adherence.update({
            where: {
                id: adherenceId,
                user_id: userId,
            },
            data: {
                status: 'skipped',
                updated_at: new Date(),
            },
            include: {
                medication: true,
                user: true,
                reminders: true,
            },
        });
        return adherence_mapper_1.AdherenceMapper.toDomain(updated);
    }
    async getStats(userId, startDate, endDate) {
        const whereClause = {
            user_id: userId,
        };
        if (startDate) {
            whereClause.scheduled_date = {
                ...whereClause.scheduled_date,
                gte: new Date(startDate),
            };
        }
        if (endDate) {
            whereClause.scheduled_date = {
                ...whereClause.scheduled_date,
                lte: new Date(endDate),
            };
        }
        const adherences = await this.prisma.adherence.findMany({
            where: whereClause,
            select: {
                status: true,
                medication: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
        return adherences.map((adherence) => ({
            status: adherence.status || 'pending',
            medication: {
                id: adherence.medication.id,
                name: adherence.medication.name,
            },
        }));
    }
    async findPendingForMissedProcessing(todayStr, cutoffTime) {
        try {
            const today = new Date(todayStr);
            const pendingFromPreviousDays = await this.prisma.adherence.findMany({
                where: {
                    status: 'pending',
                    scheduled_date: { lt: today },
                },
                include: {
                    medication: true,
                    user: true,
                    reminders: true,
                },
            });
            const todayPending = await this.prisma.adherence.findMany({
                where: {
                    status: 'pending',
                    scheduled_date: today,
                },
                include: {
                    medication: true,
                    user: true,
                    reminders: true,
                },
            });
            const skippedToday = todayPending.filter((record) => {
                const [h, m] = record.scheduled_time.split(':').map(Number);
                const scheduledDateTime = new Date(record.scheduled_date);
                scheduledDateTime.setHours(h, m, 0, 0);
                return scheduledDateTime < cutoffTime;
            });
            const allMissedRecords = [...pendingFromPreviousDays, ...skippedToday];
            return allMissedRecords.map(adherence_mapper_1.AdherenceMapper.toDomain);
        }
        catch (error) {
            console.error('Error finding pending adherence for missed processing:', error);
            throw error;
        }
    }
    async findByUserMedicationDateRange(userId, medicationId, startDate, endDate) {
        try {
            const adherences = await this.prisma.adherence.findMany({
                where: {
                    user_id: userId,
                    medication_id: medicationId,
                    scheduled_date: {
                        gte: new Date(startDate),
                        lte: new Date(endDate),
                    },
                },
                include: {
                    medication: true,
                    user: true,
                    reminders: true,
                },
                orderBy: {
                    scheduled_date: 'asc',
                },
            });
            return adherences.map(adherence_mapper_1.AdherenceMapper.toDomain);
        }
        catch (error) {
            console.error('Error finding adherence by user medication date range:', error);
            throw error;
        }
    }
    async updateStatus(adherenceId, status) {
        try {
            await this.prisma.adherence.update({
                where: { id: adherenceId },
                data: {
                    status,
                    updated_at: new Date(),
                },
            });
        }
        catch (error) {
            console.error('Error updating adherence status:', error);
            throw error;
        }
    }
};
exports.SupabaseAdherenceRepository = SupabaseAdherenceRepository;
exports.SupabaseAdherenceRepository = SupabaseAdherenceRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SupabaseAdherenceRepository);
//# sourceMappingURL=supabase-adherence.repository.js.map