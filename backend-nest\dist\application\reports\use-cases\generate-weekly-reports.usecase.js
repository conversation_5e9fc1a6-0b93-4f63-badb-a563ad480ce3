"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateWeeklyReportsUseCase = void 0;
const common_1 = require("@nestjs/common");
const notification_service_1 = require("../../../domain/reminder/services/notification.service");
let GenerateWeeklyReportsUseCase = class GenerateWeeklyReportsUseCase {
    userRepository;
    notificationService;
    subscriptionRepository;
    constructor(userRepository, notificationService, subscriptionRepository) {
        this.userRepository = userRepository;
        this.notificationService = notificationService;
        this.subscriptionRepository = subscriptionRepository;
    }
    async execute() {
        let usersProcessed = 0;
        let reportsGenerated = 0;
        let reportsSent = 0;
        let errors = 0;
        try {
            const users = await this.userRepository.findUsersWithEmailNotifications();
            console.log(`Checking ${users.length} users for premium weekly reports`);
            for (const user of users) {
                try {
                    usersProcessed++;
                    const subscription = await this.subscriptionRepository.findByUserId(user.id);
                    if (!subscription || !subscription.isPremium()) {
                        console.log(`⏭️ Skipping user ${user.email} - Premium subscription required for weekly reports`);
                        continue;
                    }
                    const success = await this.notificationService.sendWeeklyReport(user.id);
                    if (success) {
                        reportsGenerated++;
                        reportsSent++;
                        console.log(`✅ Sent premium weekly report to ${user.email}`);
                    }
                    else {
                        errors++;
                        console.error(`❌ Failed to send weekly report to ${user.email}`);
                    }
                }
                catch (userError) {
                    console.error(`❌ Error sending report for user ${user.email}:`, userError);
                    errors++;
                }
            }
            console.log(`Weekly report generation completed: ${usersProcessed} users processed, ${reportsSent} reports sent, ${errors} errors`);
            return { usersProcessed, reportsGenerated, reportsSent, errors };
        }
        catch (error) {
            console.error('Error in weekly report generation:', error);
            throw error;
        }
    }
};
exports.GenerateWeeklyReportsUseCase = GenerateWeeklyReportsUseCase;
exports.GenerateWeeklyReportsUseCase = GenerateWeeklyReportsUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('UserRepository')),
    __param(1, (0, common_1.Inject)('NotificationService')),
    __param(2, (0, common_1.Inject)('SubscriptionRepository')),
    __metadata("design:paramtypes", [Object, notification_service_1.NotificationService, Object])
], GenerateWeeklyReportsUseCase);
//# sourceMappingURL=generate-weekly-reports.usecase.js.map