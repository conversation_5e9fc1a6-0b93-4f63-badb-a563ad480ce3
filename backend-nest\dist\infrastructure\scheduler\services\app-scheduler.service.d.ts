import { OnModuleInit } from '@nestjs/common';
import { ProcessMissedAdherenceUseCase } from '../../../application/adherence/use-cases/process-missed-adherence.usecase';
import { CalculateDailyRiskScoresUseCase } from '../../../application/analytics/use-cases/calculate-daily-risk-scores.usecase';
import { GenerateWeeklyReportsUseCase } from '../../../application/reports/use-cases/generate-weekly-reports.usecase';
export declare class AppSchedulerService implements OnModuleInit {
    private readonly processMissedAdherenceUseCase;
    private readonly calculateDailyRiskScoresUseCase;
    private readonly generateWeeklyReportsUseCase;
    private readonly logger;
    constructor(processMissedAdherenceUseCase: ProcessMissedAdherenceUseCase, calculateDailyRiskScoresUseCase: CalculateDailyRiskScoresUseCase, generateWeeklyReportsUseCase: GenerateWeeklyReportsUseCase);
    onModuleInit(): void;
    handleMissedAdherenceProcessing(): Promise<void>;
    handleDailyRiskScoreCalculation(): Promise<void>;
    handleWeeklyReportGeneration(): Promise<void>;
    triggerMissedAdherenceProcessing(): Promise<import("../../../application/adherence/use-cases/process-missed-adherence.usecase").ProcessMissedAdherenceResult>;
    triggerDailyRiskScoreCalculation(): Promise<import("../../../application/analytics/use-cases/calculate-daily-risk-scores.usecase").RiskScoreCalculationResult>;
    triggerWeeklyReportGeneration(): Promise<import("../../../application/reports/use-cases/generate-weekly-reports.usecase").WeeklyReportsResult>;
}
