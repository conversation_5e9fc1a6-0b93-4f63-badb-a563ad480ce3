import { Subscription } from '../entities/subscription.entity';
export interface SubscriptionPresentation {
    status: string;
    plan: string;
    expiresAt: string | null;
    features: Record<string, boolean>;
    isActive: boolean;
    isPremium: boolean;
}
export declare class SubscriptionPresenter {
    static present(subscription: Subscription): SubscriptionPresentation;
    static presentList(subscriptions: Subscription[]): SubscriptionPresentation[];
}
