import { ReminderRepository } from 'src/domain/reminder/repositories/reminder.repository';
import { NotificationService } from 'src/domain/reminder/services/notification.service';
import { UserRepository } from 'src/domain/user/repositories/user.repository';
export declare class ScheduleRemindersUseCase {
    private readonly reminderRepository;
    private readonly notificationService;
    private readonly userRepository;
    private readonly logger;
    constructor(reminderRepository: ReminderRepository, notificationService: NotificationService, userRepository: UserRepository);
    execute(): Promise<{
        processed: number;
        sent: number;
        failed: number;
    }>;
}
