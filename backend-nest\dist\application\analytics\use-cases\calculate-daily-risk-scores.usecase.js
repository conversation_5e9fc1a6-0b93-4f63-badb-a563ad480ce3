"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculateDailyRiskScoresUseCase = void 0;
const common_1 = require("@nestjs/common");
let CalculateDailyRiskScoresUseCase = class CalculateDailyRiskScoresUseCase {
    userRepository;
    medicationRepository;
    adherenceRepository;
    riskHistoryRepository;
    constructor(userRepository, medicationRepository, adherenceRepository, riskHistoryRepository) {
        this.userRepository = userRepository;
        this.medicationRepository = medicationRepository;
        this.adherenceRepository = adherenceRepository;
        this.riskHistoryRepository = riskHistoryRepository;
    }
    async execute() {
        let usersProcessed = 0;
        let medicationsProcessed = 0;
        let riskScoresCalculated = 0;
        let errors = 0;
        try {
            const users = await this.userRepository.findAll();
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            for (const user of users) {
                try {
                    usersProcessed++;
                    if (user.subscription_status !== 'premium') {
                        continue;
                    }
                    const medications = await this.medicationRepository.findActiveMedicationsByUser(user.id);
                    for (const medication of medications) {
                        try {
                            medicationsProcessed++;
                            const riskScore = await this.calculateRiskScore(user.id, medication.id);
                            await this.storeRiskScore(user.id, medication.id, today, riskScore);
                            riskScoresCalculated++;
                            console.log(`✅ Risk score calculated: user ${user.id}, med ${medication.id}, score ${riskScore.toFixed(2)}`);
                        }
                        catch (medError) {
                            console.error(`❌ Error calculating risk for med ${medication.id} user ${user.id}:`, medError);
                            errors++;
                        }
                    }
                }
                catch (userError) {
                    console.error(`❌ Error processing user ${user.id}:`, userError);
                    errors++;
                }
            }
            console.log(`Daily risk score calculation completed: ${usersProcessed} users, ${medicationsProcessed} medications, ${riskScoresCalculated} scores calculated, ${errors} errors`);
            return {
                usersProcessed,
                medicationsProcessed,
                riskScoresCalculated,
                errors,
            };
        }
        catch (error) {
            console.error('Error in daily risk score calculation:', error);
            throw error;
        }
    }
    async calculateRiskScore(userId, medicationId) {
        try {
            const fourteenDaysAgo = new Date();
            fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);
            const adherenceRecords = await this.adherenceRepository.findByUserMedicationDateRange(userId, medicationId, fourteenDaysAgo.toISOString().split('T')[0], new Date().toISOString().split('T')[0]);
            const takenRecords = adherenceRecords.filter((record) => record.status === 'taken');
            const expectedDoses = 14;
            const takenDoses = takenRecords.length;
            const riskScore = Math.min(1, Math.max(0, 1 - takenDoses / expectedDoses));
            return riskScore;
        }
        catch (error) {
            console.error('Error calculating risk score:', error);
            return 0.5;
        }
    }
    async storeRiskScore(userId, medicationId, date, riskScore) {
        try {
            const riskRecord = {
                user_id: userId,
                medication_id: medicationId,
                date,
                risk_score: riskScore,
            };
            await this.riskHistoryRepository.create(riskRecord);
        }
        catch (error) {
            console.error('Error storing risk score:', error);
            throw error;
        }
    }
};
exports.CalculateDailyRiskScoresUseCase = CalculateDailyRiskScoresUseCase;
exports.CalculateDailyRiskScoresUseCase = CalculateDailyRiskScoresUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('UserRepository')),
    __param(1, (0, common_1.Inject)('MedicationRepository')),
    __param(2, (0, common_1.Inject)('AdherenceRepository')),
    __param(3, (0, common_1.Inject)('RiskHistoryRepository')),
    __metadata("design:paramtypes", [Object, Object, Object, Object])
], CalculateDailyRiskScoresUseCase);
//# sourceMappingURL=calculate-daily-risk-scores.usecase.js.map