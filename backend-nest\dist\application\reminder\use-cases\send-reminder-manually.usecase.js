"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendReminderManuallyUseCase = void 0;
const common_1 = require("@nestjs/common");
const notification_service_1 = require("../../../domain/reminder/services/notification.service");
let SendReminderManuallyUseCase = class SendReminderManuallyUseCase {
    reminderRepository;
    notificationService;
    userRepository;
    constructor(reminderRepository, notificationService, userRepository) {
        this.reminderRepository = reminderRepository;
        this.notificationService = notificationService;
        this.userRepository = userRepository;
    }
    async execute(reminderId, userId) {
        const reminder = await this.reminderRepository.findById(reminderId);
        if (!reminder || reminder.user_id !== userId) {
            throw new common_1.NotFoundException('Reminder not found or unauthorized');
        }
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const isPremium = user.subscription_status === 'premium' &&
            user.subscription_expires_at &&
            new Date(user.subscription_expires_at) > new Date();
        if (!isPremium) {
            throw new common_1.ForbiddenException('Premium subscription required');
        }
        try {
            await this.notificationService.sendEmailReminder(reminder);
            await this.reminderRepository.markAsSent(reminderId, 'email');
            return {
                success: true,
                message: 'Reminder sent successfully',
            };
        }
        catch (error) {
            await this.reminderRepository.markAsFailed(reminderId);
            throw new Error(`Failed to send reminder: ${error.message}`);
        }
    }
};
exports.SendReminderManuallyUseCase = SendReminderManuallyUseCase;
exports.SendReminderManuallyUseCase = SendReminderManuallyUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('ReminderRepository')),
    __param(1, (0, common_1.Inject)('NotificationService')),
    __param(2, (0, common_1.Inject)('UserRepository')),
    __metadata("design:paramtypes", [Object, notification_service_1.NotificationService, Object])
], SendReminderManuallyUseCase);
//# sourceMappingURL=send-reminder-manually.usecase.js.map