"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSubscriptionStatusUseCase = void 0;
const common_1 = require("@nestjs/common");
const subscription_domain_service_1 = require("../../../domain/subscription/services/subscription-domain.service");
const subscription_entity_1 = require("../../../domain/subscription/entities/subscription.entity");
let UpdateSubscriptionStatusUseCase = class UpdateSubscriptionStatusUseCase {
    subscriptionRepository;
    subscriptionDomainService;
    constructor(subscriptionRepository, subscriptionDomainService) {
        this.subscriptionRepository = subscriptionRepository;
        this.subscriptionDomainService = subscriptionDomainService;
    }
    async execute(command) {
        console.log('Updating subscription for user:', command.userId);
        let subscription = await this.subscriptionRepository.findByUserId(command.userId);
        if (!subscription) {
            if (command.status === subscription_entity_1.SubscriptionStatus.PREMIUM) {
                subscription = this.subscriptionDomainService.createPremiumSubscription(command.userId, command.subscriptionId, command.months || 1);
            }
            else {
                subscription = this.subscriptionDomainService.createFreeSubscription(command.userId);
            }
            await this.subscriptionRepository.create(subscription);
        }
        else {
            if (command.status === subscription_entity_1.SubscriptionStatus.PREMIUM) {
                this.subscriptionDomainService.upgradeToPremium(subscription, command.subscriptionId, command.months || 1);
            }
            else {
                this.subscriptionDomainService.downgradeToFree(subscription);
            }
            await this.subscriptionRepository.update(subscription);
        }
        const expiresAt = command.status === subscription_entity_1.SubscriptionStatus.PREMIUM
            ? this.subscriptionDomainService.calculateExpirationDate(command.months || 1)
            : null;
        const features = command.status === subscription_entity_1.SubscriptionStatus.PREMIUM
            ? subscription_entity_1.SubscriptionFeatures.createPremiumFeatures().toJson()
            : subscription_entity_1.SubscriptionFeatures.createFreeFeatures().toJson();
        await this.subscriptionRepository.updateSubscriptionStatus(command.userId, command.status, command.status, expiresAt, features, command.subscriptionId);
        console.log('Subscription updated successfully');
    }
};
exports.UpdateSubscriptionStatusUseCase = UpdateSubscriptionStatusUseCase;
exports.UpdateSubscriptionStatusUseCase = UpdateSubscriptionStatusUseCase = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('SubscriptionRepository')),
    __metadata("design:paramtypes", [Object, subscription_domain_service_1.SubscriptionDomainService])
], UpdateSubscriptionStatusUseCase);
//# sourceMappingURL=update-subscription-status.usecase.js.map