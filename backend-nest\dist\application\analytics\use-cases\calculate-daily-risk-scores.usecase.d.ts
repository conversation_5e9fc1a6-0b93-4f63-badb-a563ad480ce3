import { UserRepository } from '../../../domain/user/repositories/user.repository';
import { MedicationRepository } from '../../../domain/medication/repositories/medication.repository';
import { AdherenceRepository } from '../../../domain/adherence/repositories/adherence.repository';
import { RiskHistoryRepository } from '../../../domain/analytics/repositories/risk-history.repository';
export interface RiskScoreCalculationResult {
    usersProcessed: number;
    medicationsProcessed: number;
    riskScoresCalculated: number;
    errors: number;
}
export declare class CalculateDailyRiskScoresUseCase {
    private readonly userRepository;
    private readonly medicationRepository;
    private readonly adherenceRepository;
    private readonly riskHistoryRepository;
    constructor(userRepository: UserRepository, medicationRepository: MedicationRepository, adherenceRepository: AdherenceRepository, riskHistoryRepository: RiskHistoryRepository);
    execute(): Promise<RiskScoreCalculationResult>;
    private calculateRiskScore;
    private storeRiskScore;
}
