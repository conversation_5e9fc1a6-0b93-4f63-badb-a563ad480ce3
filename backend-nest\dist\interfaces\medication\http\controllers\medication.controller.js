"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicationController = void 0;
const common_1 = require("@nestjs/common");
const create_medication_usecase_1 = require("../../../../application/medication/use-cases/create-medication.usecase");
const create_medication_with_adherence_usecase_1 = require("../../../../application/medication/use-cases/create-medication-with-adherence.usecase");
const delete_medication_usecase_1 = require("../../../../application/medication/use-cases/delete-medication.usecase");
const find_active_medication_by_user_usecase_1 = require("../../../../application/medication/use-cases/find-active-medication-by-user.usecase");
const find_medication_by_id_usecase_1 = require("../../../../application/medication/use-cases/find-medication-by-id.usecase");
const find_medication_by_user_usecase_1 = require("../../../../application/medication/use-cases/find-medication-by-user.usecase");
const update_medication_usecase_1 = require("../../../../application/medication/use-cases/update-medication.usecase");
const get_user_id_decorator_1 = require("../../../common/decorators/get-user-id.decorator");
const jwt_auth_guard_1 = require("../../../common/guards/jwt-auth.guard");
const medication_presenter_1 = require("../../../../domain/medication/presenters/medication.presenter");
const create_medication_dto_1 = require("../../dtos/create-medication.dto");
const update_medication_dto_1 = require("../../dtos/update-medication.dto");
let MedicationController = class MedicationController {
    createMedicationUseCase;
    createMedicationWithAdherenceUseCase;
    updateMedicationUseCase;
    deleteMedicationUseCase;
    getMedicationByIdUseCase;
    getMedicationsByUserUseCase;
    getActiveMedicationsByUserUseCase;
    constructor(createMedicationUseCase, createMedicationWithAdherenceUseCase, updateMedicationUseCase, deleteMedicationUseCase, getMedicationByIdUseCase, getMedicationsByUserUseCase, getActiveMedicationsByUserUseCase) {
        this.createMedicationUseCase = createMedicationUseCase;
        this.createMedicationWithAdherenceUseCase = createMedicationWithAdherenceUseCase;
        this.updateMedicationUseCase = updateMedicationUseCase;
        this.deleteMedicationUseCase = deleteMedicationUseCase;
        this.getMedicationByIdUseCase = getMedicationByIdUseCase;
        this.getMedicationsByUserUseCase = getMedicationsByUserUseCase;
        this.getActiveMedicationsByUserUseCase = getActiveMedicationsByUserUseCase;
    }
    async getAll(userId) {
        const medications = await this.getMedicationsByUserUseCase.execute(userId);
        return medication_presenter_1.MedicationPresenter.toHttpList(medications);
    }
    async getActive(userId) {
        const medications = await this.getActiveMedicationsByUserUseCase.execute(userId);
        return medication_presenter_1.MedicationPresenter.toHttpList(medications);
    }
    async get(id) {
        const medication = await this.getMedicationByIdUseCase.execute(id);
        return medication_presenter_1.MedicationPresenter.toHttp(medication);
    }
    async create(medication, userId) {
        const created = await this.createMedicationWithAdherenceUseCase.execute({
            ...medication,
            user_id: userId,
        });
        return medication_presenter_1.MedicationPresenter.toHttp(created);
    }
    async createSimple(medication, userId) {
        const created = await this.createMedicationUseCase.execute({
            ...medication,
            user_id: userId,
        });
        return medication_presenter_1.MedicationPresenter.toHttp(created);
    }
    async update(id, userId, medication) {
        const updated = await this.updateMedicationUseCase.execute({
            ...medication,
            id,
        });
        if (!updated) {
            throw new common_1.NotFoundException('Medication not found');
        }
        if (medication.user_id !== userId) {
            throw new common_1.ForbiddenException('You are not allowed to update this medication');
        }
        return medication_presenter_1.MedicationPresenter.toHttp(updated);
    }
    async delete(id, userId) {
        const medication = await this.getMedicationByIdUseCase.execute(id);
        if (!medication) {
            throw new common_1.NotFoundException('Medication not found');
        }
        if (medication.user_id !== userId) {
            throw new common_1.ForbiddenException('You are not allowed to delete this medication');
        }
        await this.deleteMedicationUseCase.execute(id);
        return { message: 'Medication deleted successfully' };
    }
};
exports.MedicationController = MedicationController;
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MedicationController.prototype, "getAll", null);
__decorate([
    (0, common_1.Get)('active'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MedicationController.prototype, "getActive", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MedicationController.prototype, "get", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_medication_dto_1.CreateMedicationDto, String]),
    __metadata("design:returntype", Promise)
], MedicationController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('simple'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_medication_dto_1.CreateMedicationDto, String]),
    __metadata("design:returntype", Promise)
], MedicationController.prototype, "createSimple", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_medication_dto_1.UpdateMedicationDto]),
    __metadata("design:returntype", Promise)
], MedicationController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, get_user_id_decorator_1.GetUserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], MedicationController.prototype, "delete", null);
exports.MedicationController = MedicationController = __decorate([
    (0, common_1.Controller)('medications'),
    __metadata("design:paramtypes", [create_medication_usecase_1.CreateMedicationUseCase,
        create_medication_with_adherence_usecase_1.CreateMedicationWithAdherenceUseCase,
        update_medication_usecase_1.UpdateMedicationUseCase,
        delete_medication_usecase_1.DeleteMedicationUseCase,
        find_medication_by_id_usecase_1.FindMedicationByIdUseCase,
        find_medication_by_user_usecase_1.FindMedicationByUserUseCase,
        find_active_medication_by_user_usecase_1.FindActiveMedicationByUserUseCase])
], MedicationController);
//# sourceMappingURL=medication.controller.js.map