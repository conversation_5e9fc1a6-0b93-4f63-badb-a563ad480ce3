import { Reminder } from 'src/domain/reminder/entities/reminder.entity';
import { ReminderRepository } from 'src/domain/reminder/repositories/reminder.repository';
import { CreateReminderDto } from 'src/interfaces/reminder/dtos/create-reminder.dto';
export declare class CreateReminderUseCase {
    private readonly reminderRepository;
    constructor(reminderRepository: ReminderRepository);
    execute(reminder: CreateReminderDto): Promise<Reminder>;
}
