import { useQuery } from "@tanstack/react-query";
import {
  getAdherenceStats,
  getAdherenceTimeline,
  getRiskPredictions,
} from "../api/analytics";
import { useAuth } from "./useAuth";

// Get adherence statistics
export const useAnalyticsAdherenceStats = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["analytics", "adherence"],
    queryFn: getAdherenceStats,
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get adherence timeline data
export const useAdherenceTimeline = (days?: number) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["analytics", "timeline", days],
    queryFn: () => getAdherenceTimeline(days),
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get risk predictions
export const useRiskPredictions = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ["analytics", "risks"],
    queryFn: getRiskPredictions,
    enabled: !!user,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
