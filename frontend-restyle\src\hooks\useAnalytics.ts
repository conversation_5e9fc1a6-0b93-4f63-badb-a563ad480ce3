import { useQuery } from "@tanstack/react-query";
import {
  getAdherenceStats,
  getAdherenceTimeline,
  getRiskPredictions,
} from "../api/analytics";

// Get adherence statistics
export const useAnalyticsAdherenceStats = () => {
  return useQuery({
    queryKey: ["analytics", "adherence"],
    queryFn: getAdherenceStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get adherence timeline data
export const useAdherenceTimeline = (days?: number) => {
  return useQuery({
    queryKey: ["analytics", "timeline", days],
    queryFn: () => getAdherenceTimeline(days),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get risk predictions
export const useRiskPredictions = () => {
  return useQuery({
    queryKey: ["analytics", "risks"],
    queryFn: getRiskPredictions,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
