"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SendGridNotificationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendGridNotificationService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const sgMail = require("@sendgrid/mail");
const notification_service_1 = require("../../../domain/reminder/services/notification.service");
const prisma_service_1 = require("../../prisma/prisma.service");
let SendGridNotificationService = SendGridNotificationService_1 = class SendGridNotificationService extends notification_service_1.NotificationService {
    configService;
    prisma;
    logger = new common_1.Logger(SendGridNotificationService_1.name);
    constructor(configService, prisma) {
        super();
        this.configService = configService;
        this.prisma = prisma;
        const apiKey = this.configService.get('SENDGRID_API_KEY');
        if (apiKey) {
            sgMail.setApiKey(apiKey);
        }
        else {
            this.logger.warn('SENDGRID_API_KEY not configured');
        }
    }
    async sendEmailReminder(reminder) {
        try {
            if (!reminder.user?.user?.email) {
                throw new Error('User email not found');
            }
            const emailData = this.buildEmailReminderData(reminder);
            const msg = {
                to: emailData.to,
                from: this.configService.get('EMAIL_FROM') || '<EMAIL>',
                subject: emailData.subject,
                text: emailData.text,
                html: emailData.html,
            };
            await sgMail.send(msg);
            this.logger.log(`Email reminder sent to ${emailData.to}`);
            return true;
        }
        catch (error) {
            this.logger.error('Error sending email reminder:', error);
            throw error;
        }
    }
    async sendSMSReminder(reminder) {
        this.logger.warn('SMS functionality not implemented yet');
        return false;
    }
    async sendWelcomeEmail(email, name) {
        try {
            const msg = {
                to: email,
                from: this.configService.get('EMAIL_FROM') || '<EMAIL>',
                subject: 'Bienvenido a MedCare',
                text: `Hola ${name},\n\nGracias por registrarte en MedCare. Estamos aquí para ayudarte a mantener un mejor control de tu salud.\n\nCon tu cuenta gratuita, puedes:\n- Registrar tus medicamentos\n- Recibir recordatorios básicos\n- Acceder a tu historial de medicamentos\n\n¡Actualiza a Premium para obtener más beneficios!\n\nSaludos,\nTu equipo de MedCare`,
                html: `
          <h2>¡Bienvenido a MedCare!</h2>
          <p>Hola ${name},</p>
          <p>Gracias por registrarte en MedCare. Estamos aquí para ayudarte a mantener un mejor control de tu salud.</p>
          <p>Con tu cuenta gratuita, puedes:</p>
          <ul>
            <li>Registrar tus medicamentos</li>
            <li>Recibir recordatorios básicos</li>
            <li>Acceder a tu historial de medicamentos</li>
          </ul>
          <p>¡Actualiza a Premium para obtener más beneficios!</p>
          <p>Saludos,<br>Tu equipo de MedCare</p>
        `,
            };
            await sgMail.send(msg);
            this.logger.log(`Welcome email sent to ${email}`);
            return true;
        }
        catch (error) {
            this.logger.error('Error sending welcome email:', error);
            throw error;
        }
    }
    async sendSubscriptionConfirmation(email, name, plan) {
        try {
            const msg = {
                to: email,
                from: this.configService.get('EMAIL_FROM') || '<EMAIL>',
                subject: 'Confirmación de suscripción Premium',
                text: `Hola ${name},\n\n¡Gracias por actualizar a Premium!\n\nTu suscripción Premium ha sido activada. Ahora tienes acceso a:\n- Recordatorios por email y SMS\n- Notificaciones prioritarias\n- Notificaciones para familiares\n- Sonidos personalizados\n- Y mucho más...\n\nSaludos,\nTu equipo de MedCare`,
                html: `
          <h2>¡Gracias por actualizar a Premium!</h2>
          <p>Hola ${name},</p>
          <p>Tu suscripción Premium ha sido activada. Ahora tienes acceso a:</p>
          <ul>
            <li>Recordatorios por email y SMS</li>
            <li>Notificaciones prioritarias</li>
            <li>Notificaciones para familiares</li>
            <li>Sonidos personalizados</li>
            <li>Y mucho más...</li>
          </ul>
          <p>Saludos,<br>Tu equipo de MedCare</p>
        `,
            };
            await sgMail.send(msg);
            this.logger.log(`Subscription confirmation sent to ${email}`);
            return true;
        }
        catch (error) {
            this.logger.error('Error sending subscription confirmation:', error);
            throw error;
        }
    }
    async sendWeeklyReport(userId) {
        try {
            const user = await this.prisma.users.findUnique({
                where: { id: userId },
                select: { email: true, name: true },
            });
            if (!user) {
                throw new Error('User not found');
            }
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            const reminders = await this.prisma.reminders.findMany({
                where: {
                    user_id: userId,
                    scheduled_date: { gte: oneWeekAgo },
                },
                include: {
                    medication: {
                        select: { name: true, dosage: true },
                    },
                },
                orderBy: [{ scheduled_date: 'asc' }, { scheduled_time: 'asc' }],
            });
            const totalReminders = reminders.length;
            const completedReminders = reminders.filter((r) => r.status === 'sent').length;
            const adherenceRate = totalReminders > 0 ? (completedReminders / totalReminders) * 100 : 0;
            const msg = {
                to: user.email,
                from: this.configService.get('EMAIL_FROM') || '<EMAIL>',
                subject: 'Reporte Semanal de Adherencia',
                text: `Hola ${user.name},\n\nTu reporte semanal de adherencia a medicamentos:\n\nTotal de recordatorios: ${totalReminders}\nRecordatorios completados: ${completedReminders}\nTasa de adherencia: ${adherenceRate.toFixed(1)}%\n\nSaludos,\nTu equipo de MedCare`,
                html: `
          <h2>Reporte Semanal de Adherencia</h2>
          <p>Hola ${user.name},</p>
          <p>Tu reporte semanal de adherencia a medicamentos:</p>
          <ul>
            <li><strong>Total de recordatorios:</strong> ${totalReminders}</li>
            <li><strong>Recordatorios completados:</strong> ${completedReminders}</li>
            <li><strong>Tasa de adherencia:</strong> ${adherenceRate.toFixed(1)}%</li>
          </ul>
          <p>Saludos,<br>Tu equipo de MedCare</p>
        `,
            };
            await sgMail.send(msg);
            this.logger.log(`Weekly report sent to ${user.email}`);
            return true;
        }
        catch (error) {
            this.logger.error('Error sending weekly report:', error);
            throw error;
        }
    }
};
exports.SendGridNotificationService = SendGridNotificationService;
exports.SendGridNotificationService = SendGridNotificationService = SendGridNotificationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService])
], SendGridNotificationService);
//# sourceMappingURL=sendgrid-notification.service.js.map