import { Medication } from 'src/domain/medication/entities/medication.entity';
import { MedicationRepository } from 'src/domain/medication/repositories/medication.repository';
import { UpdateMedicationDto } from 'src/interfaces/medication/dtos/update-medication.dto';
export declare class UpdateMedicationUseCase {
    private readonly medicationRepository;
    constructor(medicationRepository: MedicationRepository);
    execute(medication: UpdateMedicationDto): Promise<Medication>;
}
