"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MercadoPagoPaymentService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mercadopago_1 = require("mercadopago");
let MercadoPagoPaymentService = class MercadoPagoPaymentService {
    configService;
    client;
    constructor(configService) {
        this.configService = configService;
        this.client = new mercadopago_1.MercadoPagoConfig({
            accessToken: this.configService.get('MERCADOPAGO_ACCESS_TOKEN') || '',
        });
    }
    async createCheckoutSession(request) {
        try {
            if (!request.userId) {
                throw new Error('User ID is required');
            }
            const preference = new mercadopago_1.Preference(this.client);
            const preferenceData = {
                items: [
                    {
                        id: 'premium-subscription',
                        title: 'Premium Subscription',
                        unit_price: Number(request.priceId),
                        quantity: 1,
                        currency_id: request.currency,
                    },
                ],
                payer: {
                    email: request.email,
                },
                back_urls: {
                    success: `${this.configService.get('BACKEND_URL')}/api/subscriptions/success`,
                    failure: `${this.configService.get('BACKEND_URL')}/api/subscriptions/failure`,
                    pending: `${this.configService.get('BACKEND_URL')}/api/subscriptions/pending`,
                },
                auto_return: 'approved',
                external_reference: request.userId,
                notification_url: `${this.configService.get('BACKEND_URL')}/api/subscriptions/mercadopago/webhook`,
            };
            const response = await preference.create({ body: preferenceData });
            return {
                preferenceId: response.id,
                initPoint: response.init_point,
            };
        }
        catch (error) {
            console.error('Error creating Mercado Pago subscription:', error);
            throw error;
        }
    }
    async handleWebhook(payload, signature) {
        try {
            const { collection_status, status, external_reference, payment_id, collection_id, id, 'data.id': dataId, } = payload;
            console.log('MercadoPago Webhook received:', {
                collection_status,
                status,
                external_reference,
                payment_id,
                collection_id,
                id,
                dataId,
            });
            const isApproved = collection_status === 'approved' || status === 'approved';
            return {
                success: true,
                userId: external_reference,
                paymentId: payment_id || collection_id || id || dataId,
                status: isApproved ? 'approved' : status || collection_status,
                shouldUpdateSubscription: isApproved && !!external_reference,
            };
        }
        catch (error) {
            console.error('Error handling MercadoPago webhook:', error);
            return {
                success: false,
            };
        }
    }
    async getPaymentDetails(paymentId) {
        try {
            const payment = new mercadopago_1.Payment(this.client);
            const paymentData = await payment.get({ id: paymentId });
            return {
                id: String(paymentData.id || paymentId),
                status: paymentData.status || 'unknown',
                amount: paymentData.transaction_amount || 0,
                currency: paymentData.currency_id || 'ARS',
            };
        }
        catch (error) {
            console.error('Error getting MercadoPago payment:', error);
            throw error;
        }
    }
};
exports.MercadoPagoPaymentService = MercadoPagoPaymentService;
exports.MercadoPagoPaymentService = MercadoPagoPaymentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], MercadoPagoPaymentService);
//# sourceMappingURL=mercadopago-payment.service.js.map