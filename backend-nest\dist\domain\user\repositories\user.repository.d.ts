import { UserAggregate } from '../entities/user-aggregate.entity';
import { UserSettings } from '../entities/user-settings.entity';
import { User } from '../entities/user.entity';
export interface UserRepository {
    getMyProfile(id: string): Promise<UserAggregate | null>;
    findById(id: string): Promise<User | null>;
    update(userAggregate: UserAggregate): Promise<UserAggregate>;
    delete(id: string): Promise<void>;
    updateSettings(userId: string, settings: Partial<UserSettings>): Promise<UserSettings>;
    findAll(): Promise<User[]>;
    findUsersWithEmailNotifications(): Promise<User[]>;
}
