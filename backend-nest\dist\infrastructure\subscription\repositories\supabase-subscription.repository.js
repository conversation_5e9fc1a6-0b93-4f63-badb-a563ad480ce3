"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseSubscriptionRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const subscription_mapper_1 = require("../../../domain/subscription/mappers/subscription.mapper");
let SupabaseSubscriptionRepository = class SupabaseSubscriptionRepository {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findByUserId(userId) {
        try {
            const user = await this.prisma.users.findUnique({
                where: { id: userId },
                select: {
                    id: true,
                    subscription_status: true,
                    subscription_plan: true,
                    subscription_expires_at: true,
                    subscription_features: true,
                    created_at: true,
                    updated_at: true,
                },
            });
            if (!user) {
                return null;
            }
            return subscription_mapper_1.SubscriptionMapper.fromUserData(user);
        }
        catch (error) {
            console.error('Error finding subscription by user ID:', error);
            throw error;
        }
    }
    async updateSubscriptionStatus(userId, status, plan, expiresAt, features, paymentProviderId) {
        try {
            await this.prisma.users.update({
                where: { id: userId },
                data: {
                    subscription_status: status,
                    subscription_plan: plan,
                    subscription_expires_at: expiresAt,
                    subscription_features: features,
                    updated_at: new Date(),
                },
            });
        }
        catch (error) {
            console.error('Error updating subscription status:', error);
            throw error;
        }
    }
    async create(subscription) {
        try {
            await this.updateSubscriptionStatus(subscription.userId, subscription.status, subscription.plan, subscription.expiresAt, subscription.features.toJson(), subscription.paymentProviderId ?? undefined);
            return subscription;
        }
        catch (error) {
            console.error('Error creating subscription:', error);
            throw error;
        }
    }
    async update(subscription) {
        try {
            await this.updateSubscriptionStatus(subscription.userId, subscription.status, subscription.plan, subscription.expiresAt, subscription.features.toJson(), subscription.paymentProviderId ?? undefined);
            return subscription;
        }
        catch (error) {
            console.error('Error updating subscription:', error);
            throw error;
        }
    }
};
exports.SupabaseSubscriptionRepository = SupabaseSubscriptionRepository;
exports.SupabaseSubscriptionRepository = SupabaseSubscriptionRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SupabaseSubscriptionRepository);
//# sourceMappingURL=supabase-subscription.repository.js.map