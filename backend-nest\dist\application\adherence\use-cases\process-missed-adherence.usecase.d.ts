import { AdherenceRepository } from '../../../domain/adherence/repositories/adherence.repository';
export interface ProcessMissedAdherenceResult {
    processed: number;
    updated: number;
    failed: number;
}
export declare class ProcessMissedAdherenceUseCase {
    private readonly adherenceRepository;
    constructor(adherenceRepository: AdherenceRepository);
    execute(): Promise<ProcessMissedAdherenceResult>;
}
