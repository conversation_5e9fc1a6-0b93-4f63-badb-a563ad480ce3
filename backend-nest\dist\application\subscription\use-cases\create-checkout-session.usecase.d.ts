import { PaymentProvider, PaymentProviderType, CheckoutSessionResponse } from '../../../domain/subscription/services/payment-provider.interface';
export interface CreateCheckoutSessionCommand {
    userId: string;
    priceId: string;
    paymentProvider: PaymentProviderType;
    currency?: string;
    email: string;
}
export declare class CreateCheckoutSessionUseCase {
    private readonly stripeProvider;
    private readonly mercadoPagoProvider;
    constructor(stripeProvider: PaymentProvider, mercadoPagoProvider: PaymentProvider);
    execute(command: CreateCheckoutSessionCommand): Promise<CheckoutSessionResponse>;
    private getPaymentProvider;
}
