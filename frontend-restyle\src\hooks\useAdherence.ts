import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getAdherenceHistory,
  confirmDose,
  skipDose,
  getAdherenceStats,
  ConfirmDoseDto,
  SkipDoseDto,
} from "../api/adherence";
import { toast } from "sonner";

// Get adherence history
export const useAdherenceHistory = (date?: string) => {
  return useQuery({
    queryKey: ["adherence", "history", date],
    queryFn: () => getAdherenceHistory(date),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Get adherence statistics
export const useAdherenceStats = () => {
  return useQuery({
    queryKey: ["adherence", "stats"],
    queryFn: getAdherenceStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Confirm dose taken
export const useConfirmDose = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: confirmDose,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["adherence"] });
      queryClient.invalidateQueries({ queryKey: ["analytics"] });
      toast.success("Dose confirmed successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to confirm dose");
    },
  });
};

// Skip dose
export const useSkipDose = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: skipDose,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["adherence"] });
      queryClient.invalidateQueries({ queryKey: ["analytics"] });
      toast.success("Dose skipped");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to skip dose");
    },
  });
};
