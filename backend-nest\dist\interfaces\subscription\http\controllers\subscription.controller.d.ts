import { Response } from 'express';
import { CreateCheckoutSessionDto } from '../dtos/create-checkout-session.dto';
import { SubscriptionStatusDto } from '../dtos/subscription-status.dto';
import { CreateCheckoutSessionUseCase } from '../../../../application/subscription/use-cases/create-checkout-session.usecase';
import { GetSubscriptionStatusUseCase } from '../../../../application/subscription/use-cases/get-subscription-status.usecase';
import { HandleWebhookUseCase } from '../../../../application/subscription/use-cases/handle-webhook.usecase';
import { HandleSuccessRedirectUseCase } from '../../../../application/subscription/use-cases/handle-success-redirect.usecase';
export declare class SubscriptionController {
    private readonly createCheckoutSessionUseCase;
    private readonly getSubscriptionStatusUseCase;
    private readonly handleWebhookUseCase;
    private readonly handleSuccessRedirectUseCase;
    constructor(createCheckoutSessionUseCase: CreateCheckoutSessionUseCase, getSubscriptionStatusUseCase: GetSubscriptionStatusUseCase, handleWebhookUseCase: HandleWebhookUseCase, handleSuccessRedirectUseCase: HandleSuccessRedirectUseCase);
    createCheckoutSession(userId: string, createCheckoutSessionDto: CreateCheckoutSessionDto): Promise<import("../../../../domain/subscription/services/payment-provider.interface").CheckoutSessionResponse>;
    getSubscriptionStatus(userId: string): Promise<SubscriptionStatusDto>;
    handleStripeWebhook(payload: Buffer, signature: string): Promise<{
        received: boolean;
    }>;
    handleMercadoPagoWebhook(body: any, query: any): Promise<{
        received: boolean;
    }>;
    handleSuccess(query: any, res: Response): Promise<void>;
    handleFailure(externalReference: string, res: Response): Promise<void>;
    handlePending(externalReference: string, res: Response): Promise<void>;
}
