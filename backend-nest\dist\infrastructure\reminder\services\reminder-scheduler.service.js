"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ReminderSchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReminderSchedulerService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const schedule_reminders_usecase_1 = require("../../../application/reminder/use-cases/schedule-reminders.usecase");
let ReminderSchedulerService = ReminderSchedulerService_1 = class ReminderSchedulerService {
    scheduleRemindersUseCase;
    logger = new common_1.Logger(ReminderSchedulerService_1.name);
    constructor(scheduleRemindersUseCase) {
        this.scheduleRemindersUseCase = scheduleRemindersUseCase;
    }
    onModuleInit() {
        this.logger.log('Reminder Scheduler Service initialized');
    }
    async handleReminderCheck() {
        try {
            this.logger.log('Running reminder check job');
            const result = await this.scheduleRemindersUseCase.execute();
            this.logger.log(`Reminder job completed: ${JSON.stringify(result)}`);
        }
        catch (error) {
            this.logger.error(`Error in reminder job: ${error.message}`, error.stack);
        }
    }
    async triggerReminderCheck() {
        this.logger.log('Manually triggering reminder check');
        return this.scheduleRemindersUseCase.execute();
    }
};
exports.ReminderSchedulerService = ReminderSchedulerService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ReminderSchedulerService.prototype, "handleReminderCheck", null);
exports.ReminderSchedulerService = ReminderSchedulerService = ReminderSchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [schedule_reminders_usecase_1.ScheduleRemindersUseCase])
], ReminderSchedulerService);
//# sourceMappingURL=reminder-scheduler.service.js.map