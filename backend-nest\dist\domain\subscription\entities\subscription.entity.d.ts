export declare class Subscription {
    readonly id: string;
    readonly userId: string;
    status: SubscriptionStatus;
    plan: SubscriptionPlan;
    expiresAt: Date | null;
    features: SubscriptionFeatures;
    paymentProviderId?: string | null | undefined;
    createdAt?: Date | undefined;
    updatedAt?: Date | undefined;
    constructor(id: string, userId: string, status: SubscriptionStatus, plan: SubscriptionPlan, expiresAt: Date | null, features: SubscriptionFeatures, paymentProviderId?: string | null | undefined, createdAt?: Date | undefined, updatedAt?: Date | undefined);
    isActive(): boolean;
    isPremium(): boolean;
    hasFeature(feature: keyof SubscriptionFeatures): boolean;
    updateToPremium(expirationDate: Date, paymentProviderId?: string): void;
    downgradeToFree(): void;
    isExpired(): boolean;
}
export declare enum SubscriptionStatus {
    FREE = "free",
    PREMIUM = "premium"
}
export declare enum SubscriptionPlan {
    FREE = "free",
    PREMIUM = "premium"
}
export declare class SubscriptionFeatures {
    smsReminders: boolean;
    customSounds: boolean;
    priorityNotifications: boolean;
    familyNotifications: boolean;
    weeklyReports: boolean;
    advancedAnalytics: boolean;
    riskScoring: boolean;
    constructor(smsReminders?: boolean, customSounds?: boolean, priorityNotifications?: boolean, familyNotifications?: boolean, weeklyReports?: boolean, advancedAnalytics?: boolean, riskScoring?: boolean);
    static createPremiumFeatures(): SubscriptionFeatures;
    static createFreeFeatures(): SubscriptionFeatures;
    static fromJson(json: any): SubscriptionFeatures;
    toJson(): Record<string, boolean>;
}
