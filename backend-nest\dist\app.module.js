"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const configuration_1 = require("./configuration");
const medication_module_1 = require("./interfaces/medication/medication.module");
const adherence_module_1 = require("./interfaces/adherence/adherence.module");
const user_module_1 = require("./interfaces/user/user.module");
const reminder_module_1 = require("./interfaces/reminder/reminder.module");
const subscription_module_1 = require("./interfaces/subscription/subscription.module");
const scheduler_module_1 = require("./interfaces/scheduler/scheduler.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [configuration_1.default],
            }),
            user_module_1.UserModule,
            medication_module_1.MedicationModule,
            adherence_module_1.AdherenceModule,
            reminder_module_1.ReminderModule,
            subscription_module_1.SubscriptionModule,
            scheduler_module_1.SchedulerModule,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map