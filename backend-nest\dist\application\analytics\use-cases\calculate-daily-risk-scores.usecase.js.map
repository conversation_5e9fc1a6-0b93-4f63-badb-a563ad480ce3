{"version": 3, "file": "calculate-daily-risk-scores.usecase.js", "sourceRoot": "", "sources": ["../../../../src/application/analytics/use-cases/calculate-daily-risk-scores.usecase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AAiB7C,IAAM,+BAA+B,GAArC,MAAM,+BAA+B;IAEG;IAE1B;IAEA;IAEA;IAPnB,YAC6C,cAA8B,EAExD,oBAA0C,EAE1C,mBAAwC,EAExC,qBAA4C;QANlB,mBAAc,GAAd,cAAc,CAAgB;QAExD,yBAAoB,GAApB,oBAAoB,CAAsB;QAE1C,wBAAmB,GAAnB,mBAAmB,CAAqB;QAExC,0BAAqB,GAArB,qBAAqB,CAAuB;IAC5D,CAAC;IAEJ,KAAK,CAAC,OAAO;QACX,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAC7B,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAC7B,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;YAClD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAE9C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,cAAc,EAAE,CAAC;oBAGjB,IAAI,IAAI,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;wBAC3C,SAAS;oBACX,CAAC;oBAED,MAAM,WAAW,GACf,MAAM,IAAI,CAAC,oBAAoB,CAAC,2BAA2B,CACzD,IAAI,CAAC,EAAE,CACR,CAAC;oBAEJ,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;wBACrC,IAAI,CAAC;4BACH,oBAAoB,EAAE,CAAC;4BAGvB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC7C,IAAI,CAAC,EAAE,EACP,UAAU,CAAC,EAAE,CACd,CAAC;4BAGF,MAAM,IAAI,CAAC,cAAc,CACvB,IAAI,CAAC,EAAE,EACP,UAAU,CAAC,EAAE,EACb,KAAK,EACL,SAAS,CACV,CAAC;4BAEF,oBAAoB,EAAE,CAAC;4BACvB,OAAO,CAAC,GAAG,CACT,iCAAiC,IAAI,CAAC,EAAE,SAAS,UAAU,CAAC,EAAE,WAAW,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAChG,CAAC;wBACJ,CAAC;wBAAC,OAAO,QAAQ,EAAE,CAAC;4BAClB,OAAO,CAAC,KAAK,CACX,oCAAoC,UAAU,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,GAAG,EACpE,QAAQ,CACT,CAAC;4BACF,MAAM,EAAE,CAAC;wBACX,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,SAAS,EAAE,CAAC;oBACnB,OAAO,CAAC,KAAK,CAAC,2BAA2B,IAAI,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;oBAChE,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CACT,2CAA2C,cAAc,WAAW,oBAAoB,iBAAiB,oBAAoB,uBAAuB,MAAM,SAAS,CACpK,CAAC;YAEF,OAAO;gBACL,cAAc;gBACd,oBAAoB;gBACpB,oBAAoB;gBACpB,MAAM;aACP,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,MAAc,EACd,YAAoB;QAEpB,IAAI,CAAC;YAEH,MAAM,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;YACnC,eAAe,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;YAExD,MAAM,gBAAgB,GACpB,MAAM,IAAI,CAAC,mBAAmB,CAAC,6BAA6B,CAC1D,MAAM,EACN,YAAY,EACZ,eAAe,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAC3C,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACvC,CAAC;YAGJ,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAC1C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,OAAO,CACtC,CAAC;YAIF,MAAM,aAAa,GAAG,EAAE,CAAC;YACzB,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC;YAGvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CACxB,CAAC,EACD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,GAAG,aAAa,CAAC,CAC5C,CAAC;YAEF,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,GAAG,CAAC;QACb,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,MAAc,EACd,YAAoB,EACpB,IAAY,EACZ,SAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,UAAU,GAAsB;gBACpC,OAAO,EAAE,MAAM;gBACf,aAAa,EAAE,YAAY;gBAC3B,IAAI;gBACJ,UAAU,EAAE,SAAS;aACtB,CAAC;YAGF,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AArJY,0EAA+B;0CAA/B,+BAA+B;IAD3C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,gBAAgB,CAAC,CAAA;IACxB,WAAA,IAAA,eAAM,EAAC,sBAAsB,CAAC,CAAA;IAE9B,WAAA,IAAA,eAAM,EAAC,qBAAqB,CAAC,CAAA;IAE7B,WAAA,IAAA,eAAM,EAAC,uBAAuB,CAAC,CAAA;;GAPvB,+BAA+B,CAqJ3C"}