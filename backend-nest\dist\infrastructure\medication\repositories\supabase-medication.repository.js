"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseMedicationRepository = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const medication_mapper_1 = require("../../../domain/medication/mappers/medication.mapper");
let SupabaseMedicationRepository = class SupabaseMedicationRepository {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(medication) {
        const created = await this.prisma.medications.create({
            data: {
                name: medication.name,
                user_id: medication.user_id,
                dosage: medication.dosage,
                frequency: medication.frequency,
                scheduled_times: medication.scheduled_times,
                instructions: medication.instructions,
                start_date: medication.start_date
                    ? new Date(medication.start_date)
                    : undefined,
                end_date: medication.end_date
                    ? new Date(medication.end_date)
                    : undefined,
                refill_reminder: medication.refill_reminder,
                side_effects_to_watch: medication.side_effects_to_watch,
                active: medication.active,
                medication_type: medication.medication_type,
                image_url: medication.image_url,
            },
        });
        return medication_mapper_1.MedicationMapper.toDomain(created);
    }
    async update(medication) {
        const updateData = { ...medication };
        if (updateData.dosage)
            updateData.dosage = updateData.dosage;
        if (updateData.frequency)
            updateData.frequency = updateData.frequency;
        if (updateData.refill_reminder)
            updateData.refill_reminder = updateData.refill_reminder;
        const updated = await this.prisma.medications.update({
            where: { id: medication.id },
            data: {
                ...updateData,
                start_date: updateData.start_date
                    ? new Date(updateData.start_date)
                    : undefined,
                end_date: updateData.end_date
                    ? new Date(updateData.end_date)
                    : undefined,
            },
        });
        return medication_mapper_1.MedicationMapper.toDomain(updated);
    }
    async delete(id) {
        await this.prisma.medications.delete({
            where: { id },
        });
        return { message: 'Medication deleted successfully' };
    }
    async findById(id) {
        const found = await this.prisma.medications.findUnique({
            where: { id },
        });
        if (!found)
            return null;
        return medication_mapper_1.MedicationMapper.toDomain(found);
    }
    async findByUser(userId) {
        const found = await this.prisma.medications.findMany({
            where: { user_id: userId },
        });
        return found.map((med) => medication_mapper_1.MedicationMapper.toDomain(med));
    }
    async findActiveByUser(userId) {
        const found = await this.prisma.medications.findMany({
            where: { user_id: userId, active: true },
        });
        return found.map((med) => medication_mapper_1.MedicationMapper.toDomain(med));
    }
    async findActiveMedicationsByUser(userId) {
        return this.findActiveByUser(userId);
    }
};
exports.SupabaseMedicationRepository = SupabaseMedicationRepository;
exports.SupabaseMedicationRepository = SupabaseMedicationRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], SupabaseMedicationRepository);
//# sourceMappingURL=supabase-medication.repository.js.map