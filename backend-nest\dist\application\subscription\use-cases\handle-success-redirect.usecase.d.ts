import { UpdateSubscriptionStatusUseCase } from './update-subscription-status.usecase';
export interface HandleSuccessRedirectCommand {
    collectionStatus?: string;
    status?: string;
    externalReference?: string;
    paymentId?: string;
    collectionId?: string;
}
export interface SuccessRedirectResult {
    redirectUrl: string;
}
export declare class HandleSuccessRedirectUseCase {
    private readonly updateSubscriptionStatusUseCase;
    constructor(updateSubscriptionStatusUseCase: UpdateSubscriptionStatusUseCase);
    execute(command: HandleSuccessRedirectCommand): Promise<SuccessRedirectResult>;
}
