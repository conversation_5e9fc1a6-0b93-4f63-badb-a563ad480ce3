import { OnModuleInit } from '@nestjs/common';
import { ScheduleRemindersUseCase } from 'src/application/reminder/use-cases/schedule-reminders.usecase';
export declare class ReminderSchedulerService implements OnModuleInit {
    private readonly scheduleRemindersUseCase;
    private readonly logger;
    constructor(scheduleRemindersUseCase: ScheduleRemindersUseCase);
    onModuleInit(): void;
    handleReminderCheck(): Promise<void>;
    triggerReminderCheck(): Promise<{
        processed: number;
        sent: number;
        failed: number;
    }>;
}
