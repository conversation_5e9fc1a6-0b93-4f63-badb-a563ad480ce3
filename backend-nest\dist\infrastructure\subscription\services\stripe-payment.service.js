"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripePaymentService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const stripe_1 = require("stripe");
let StripePaymentService = class StripePaymentService {
    configService;
    stripe;
    constructor(configService) {
        this.configService = configService;
        this.stripe = new stripe_1.default(this.configService.get('STRIPE_SECRET_KEY') || '');
    }
    async createCheckoutSession(request) {
        try {
            const session = await this.stripe.checkout.sessions.create({
                payment_method_types: ['card'],
                line_items: [
                    {
                        price: request.priceId,
                        quantity: 1,
                    },
                ],
                mode: 'subscription',
                success_url: `${this.configService.get('FRONTEND_URL')}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
                cancel_url: `${this.configService.get('FRONTEND_URL')}/subscription`,
                client_reference_id: request.userId,
                metadata: {
                    userId: request.userId,
                },
            });
            return {
                sessionId: session.id,
                url: session.url || undefined,
            };
        }
        catch (error) {
            console.error('Error creating Stripe checkout session:', error);
            throw error;
        }
    }
    async handleWebhook(payload, signature) {
        try {
            const event = this.stripe.webhooks.constructEvent(payload, signature || '', this.configService.get('STRIPE_WEBHOOK_SECRET') || '');
            switch (event.type) {
                case 'checkout.session.completed':
                    return await this.handleCheckoutSessionCompleted(event.data.object);
                case 'customer.subscription.updated':
                    return await this.handleSubscriptionUpdated(event.data.object);
                case 'customer.subscription.deleted':
                    return await this.handleSubscriptionDeleted(event.data.object);
                default:
                    return { success: true };
            }
        }
        catch (error) {
            console.error('Error handling Stripe webhook:', error);
            return { success: false };
        }
    }
    async handleCheckoutSessionCompleted(session) {
        const userId = session.client_reference_id;
        const subscriptionId = session.subscription;
        return {
            success: true,
            userId,
            paymentId: subscriptionId,
            status: 'completed',
            shouldUpdateSubscription: true,
        };
    }
    async handleSubscriptionUpdated(subscription) {
        const userId = subscription.metadata?.userId;
        const status = subscription.status;
        return {
            success: true,
            userId,
            paymentId: subscription.id,
            status,
            shouldUpdateSubscription: status === 'active',
        };
    }
    async handleSubscriptionDeleted(subscription) {
        const userId = subscription.metadata?.userId;
        return {
            success: true,
            userId,
            paymentId: subscription.id,
            status: 'deleted',
            shouldUpdateSubscription: true,
        };
    }
    async getPaymentDetails(paymentId) {
        try {
            const subscription = {
                id: paymentId,
                status: 'active',
                items: {
                    data: [{ price: { unit_amount: 1500, currency: 'usd' } }],
                },
            };
            return {
                id: subscription.id,
                status: subscription.status,
                amount: subscription.items.data[0].price.unit_amount,
                currency: subscription.items.data[0].price.currency,
            };
        }
        catch (error) {
            console.error('Error getting Stripe payment details:', error);
            throw error;
        }
    }
};
exports.StripePaymentService = StripePaymentService;
exports.StripePaymentService = StripePaymentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], StripePaymentService);
//# sourceMappingURL=stripe-payment.service.js.map