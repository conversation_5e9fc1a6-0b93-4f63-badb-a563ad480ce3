import { Subscription, SubscriptionFeatures } from '../entities/subscription.entity';
export declare class SubscriptionDomainService {
    calculateExpirationDate(months?: number): Date;
    createPremiumSubscription(userId: string, paymentProviderId?: string, months?: number): Subscription;
    createFreeSubscription(userId: string): Subscription;
    upgradeToPremium(subscription: Subscription, paymentProviderId?: string, months?: number): void;
    downgradeToFree(subscription: Subscription): void;
    isSubscriptionExpired(subscription: Subscription): boolean;
    shouldRenewSubscription(subscription: Subscription, daysBeforeExpiry?: number): boolean;
    validateSubscriptionForFeature(subscription: Subscription, feature: keyof SubscriptionFeatures): boolean;
}
